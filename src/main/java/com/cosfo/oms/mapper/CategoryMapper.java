package com.cosfo.oms.mapper;

import com.cosfo.oms.model.po.Category;
import com.cosfo.oms.model.vo.CategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CategoryMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(Category record);

    int insertSelective(Category record);

    Category selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Category record);

    int updateByPrimaryKey(Category record);

    /**
     * 根据类目名称查询类目
     *
     * @param name
     * @return
     */
    Category selectByName(@Param("name") String name);

    /**
     * 根据父级类目Id查询类目
     *
     * @param parentId
     * @return
     */
    List<Category> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 删除类目
     *
     * @param categoryIds
     * @return
     */
    Integer delete(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 查询所有类目
     *
     * @return
     */
    List<CategoryVO> list();

    /**
     * 根据三级类目Id查询类目树
     *
     * @param categoryId
     * @return
     */
    String queryCategoryTreeByThirdCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 查询父级类目集合
     *
     * @param categoryIds
     * @return
     */
    List<CategoryVO> selectByParentCategory(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 查询子集类目ID集合
     *
     * @param categoryIds
     * @return
     */
    List<CategoryVO> selectByChildCategory(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 模糊查詢
     *
     * @param name
     * @return
     */
    List<CategoryVO> selectCategoryVOByName(@Param("name") String name);

    /**
     * 同级类目下不能有相同名称
     *
     * @param parentId
     * @param id
     * @param category
     * @return
     */
    boolean hasEqualsName(@Param("parentId") Long parentId, @Param("id") Integer id, @Param("category") String category);
}