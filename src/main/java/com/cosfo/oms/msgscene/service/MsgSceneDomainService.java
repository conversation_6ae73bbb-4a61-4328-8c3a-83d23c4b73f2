package com.cosfo.oms.msgscene.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.message.client.msgtemplate.resp.MsgPublicTemplateWechatListResultResp;
import com.cosfo.message.client.msgtemplate.resp.MsgSceneWechatDetailResultResp;
import com.cosfo.oms.client.common.MsgSceneEnum.SceneMappingStatus;
import com.cosfo.oms.common.exception.DefaultServiceException;
import com.cosfo.oms.common.utils.PageInfoHelper;
import com.cosfo.oms.facade.MessageCenterServiceFacade;
import com.cosfo.oms.facade.convert.MsgTemplateConverter;
import com.cosfo.oms.msgscene.convert.MsgSceneConverter;
import com.cosfo.oms.msgscene.dao.MsgSceneDao;
import com.cosfo.oms.msgscene.dao.MsgSceneTenantMappingDao;
import com.cosfo.oms.msgscene.dto.MsgSceneQueryDTO;
import com.cosfo.oms.msgscene.model.MsgScene;
import com.cosfo.oms.msgscene.model.MsgSceneTenantMapping;
import com.cosfo.oms.msgscene.vo.MsgSceneDetailVO;
import com.cosfo.oms.msgscene.vo.MsgSceneTenantMappingVO;
import com.cosfo.oms.msgscene.vo.MsgSceneWechatDetailVO;
import com.cosfo.oms.msgscene.vo.TemplateIdQueryVO;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.cosfo.oms.wechat.model.dto.MsgSceneCountDTO;
import com.cosfo.oms.wechat.model.dto.MsgSceneDTO;
import com.cosfo.oms.wechat.model.dto.MsgSceneWechatDetailDTO;
import com.cosfo.oms.wechat.model.dto.MsgUserSceneDTO;
import com.cosfo.oms.wechat.model.vo.MsgSceneUserWechatVO;
import com.cosfo.oms.wechat.model.vo.MsgSceneWechatVO;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/10 14:56
 */
@Slf4j
@Component
public class MsgSceneDomainService {
    @Resource
    private MsgSceneDao msgSceneDao;
    @Resource
    private MsgSceneTenantMappingDao msgSceneTenantMappingDao;
    @Resource
    private MessageCenterServiceFacade messageCenterServiceFacade;
    @Resource
    private TenantInfoService tenantInfoService;


    private Integer pageSize = 10;
    private Integer pageIndex = 1;
    private Long pId = 0L;

    /**
     * 按帆台模板id查询关联场景总数
     * @param id
     * @return
     */
    public int countScene(Long id){
        int countScene = msgSceneDao.countScene(id);
        return countScene;
    }

    public PageInfo<MsgSceneDTO> getWechatMsgSceneList(MsgSceneWechatVO msgSceneWechatVO){
        if (ObjectUtils.isEmpty(msgSceneWechatVO)){
            msgSceneWechatVO = new MsgSceneWechatVO();
            msgSceneWechatVO.setPageIndex(pageIndex);
            msgSceneWechatVO.setPageSize(pageSize);
        }
        if (ObjectUtils.isNotEmpty(msgSceneWechatVO.getPrivateFlag())){
            List<MsgSceneTenantMapping> mappingList = msgSceneTenantMappingDao.getMappingGroupBySceneId();
            List<Long> sceneIdList = mappingList.stream().map(item -> item.getSceneId()).collect(Collectors.toList());
            msgSceneWechatVO.setSceneIdList(sceneIdList);
        }
        PageInfo<MsgScene> wechatMsgScenePage = msgSceneDao.getWechatMsgSceneList(msgSceneWechatVO);
        List<Long> idList = wechatMsgScenePage.getList().stream().map(item -> item.getUpdater()).collect(Collectors.toList());
        Map<Long, TenantResultResp> tenantVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(idList)){
            List<TenantResultResp> tenantInfoList = tenantInfoService.getTenantInfoList(idList);
            tenantVOMap = tenantInfoList.stream().collect(Collectors.toMap(TenantResultResp::getId, item -> item));
        }
        List<MsgSceneDTO> voList = new LinkedList<>();
        for (MsgScene msgScene : wechatMsgScenePage.getList()) {
            MsgSceneDTO msgSceneDTO = new MsgSceneDTO();
            msgSceneDTO = MsgSceneConverter.msgScene2DTO(msgScene, tenantVOMap);
            voList.add(msgSceneDTO);
        }
        PageInfo<MsgSceneDTO> pageInfo = (PageInfo<MsgSceneDTO>) MsgTemplateConverter.turnToPageInfo(new PageInfo<MsgSceneDTO>(), wechatMsgScenePage);
        pageInfo.setList(voList);
        return pageInfo;
    }


    public MsgSceneWechatDetailDTO getWechatMsgSceneTemplateDetail(Long id){
        // 查询帆台模板池
        MsgPublicTemplateWechatListResultResp template = messageCenterServiceFacade.getWechatPublicMsgTemplateById(id);
        // 查询关联场景列表
        List<MsgScene> sceneList = new LinkedList<>();
        List<Long> idList = new LinkedList<>();
        List<MsgSceneDetailVO> detailVOList = new LinkedList<>();
        sceneList = msgSceneDao.getRelateWechatMsgSceneList(id);
        if (!CollectionUtils.isEmpty(sceneList)){
            idList = sceneList.stream().map(item -> item.getUpdater()).collect(Collectors.toList());
            detailVOList = sceneList.stream().map(MsgSceneConverter::msgScene2DetailVO).collect(Collectors.toList());
        }
        Map<Long, TenantResultResp> tenantVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(idList)) {
            List<TenantResultResp> tenantInfoList = tenantInfoService.getTenantInfoList(idList);
            tenantVOMap = tenantInfoList.stream().collect(Collectors.toMap(TenantResultResp::getId, item -> item));
        }
        MsgSceneWechatDetailVO wechatDetailVO = MsgSceneConverter.msgScene2MsgSceneWechatDetailVO(template, detailVOList);
        MsgSceneWechatDetailResultResp resultResp = MsgSceneConverter.msgSceneWechatDetailVO2Resp(wechatDetailVO);
        MsgSceneWechatDetailDTO detailDTO = MsgSceneConverter.msgSceneWechatDetailResultResp2DTO(resultResp, tenantVOMap);
        return detailDTO;
    }

    public MsgSceneDTO getWechatMsgSceneDetail(Long id){
        // 按id查询场景
        MsgScene msgScene = msgSceneDao.getById(id);
        TenantResultResp tenantInfo = new TenantResultResp();
        if (ObjectUtils.isNotEmpty(msgScene)){
            tenantInfo = tenantInfoService.getTenantInfo(msgScene.getUpdater());
        }
        Map<Long, TenantResultResp> tenantVOMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(tenantInfo)){
            tenantVOMap.put(tenantInfo.getId(), tenantInfo);
        }
        MsgSceneDTO msgSceneDTO = MsgSceneConverter.msgScene2DTO(msgScene, tenantVOMap);
        return msgSceneDTO;
    }

    public MsgSceneCountDTO getWechatMsgSceneCount(Long id){
        // 已关联场景数
        int countLink = msgSceneTenantMappingDao.countById(id);
        // 所有启用场景数
        int countAll = msgSceneTenantMappingDao.countById(id, SceneMappingStatus.ABLED.getValue());
        MsgSceneCountDTO msgSceneCountDTO = new MsgSceneCountDTO();
        msgSceneCountDTO.setCount(countLink);
        msgSceneCountDTO.setEnableCount(countAll);
        return msgSceneCountDTO;
    }

    public PageInfo<MsgUserSceneDTO> getUserWechatMsgSceneList(MsgSceneUserWechatVO msgSceneUserWechatVO){
        Page<MsgScene> page = new Page<>();
        if (ObjectUtils.isEmpty(msgSceneUserWechatVO.getPageIndex())){
            page.setCurrent(pageIndex);
        }else {
            page.setCurrent(msgSceneUserWechatVO.getPageIndex());
        }
        if (ObjectUtils.isEmpty(msgSceneUserWechatVO.getPageSize())){
            page.setSize(pageSize);
        }else {
            page.setSize(msgSceneUserWechatVO.getPageSize());
        }
        // 查出符合条件的mapping记录
        List<MsgSceneTenantMapping> mappingList = msgSceneTenantMappingDao.getMappingList(msgSceneUserWechatVO);
        // 取出这些记录的场景id集合
        Set<Long> sceneIdSet = new HashSet<>();
        List<Long> uIdList = new LinkedList<>();
        Map<Long, MsgSceneTenantMapping> mappingMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(mappingList)){
            sceneIdSet = mappingList.stream().map(item -> item.getSceneId()).collect(Collectors.toSet());
            mappingMap = mappingList.stream().collect(Collectors.toMap(MsgSceneTenantMapping::getSceneId, item -> item));
            // 取出这些场景对应的uId集合
            uIdList = mappingList.stream().map(item -> item.getUId()).collect(Collectors.toList());
        }else {
            return PageInfoHelper.createPageInfo(new LinkedList<>(),msgSceneUserWechatVO.getPageSize());
        }
        // 根据条件分页查询出场景列表
        Page<MsgScene> msgScenePage = msgSceneDao.selectPage(page, sceneIdSet, msgSceneUserWechatVO);
        List<MsgScene> sceneList = msgScenePage.getRecords();
        // 取出这些场景的对应帆台模板id集合
        List<Long> templateIdList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(sceneList)) {
            templateIdList = sceneList.stream().map(item -> item.getTemplateId()).collect(Collectors.toList());
        }
        List<MsgPublicTemplateWechatListResultResp> wechatListByCondition = messageCenterServiceFacade.getWechatPublicMsgTemplateByIdList(templateIdList);
        List<TenantResultResp> tenantVOList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(uIdList)){
            tenantVOList = tenantInfoService.getTenantInfoList(uIdList);
        }
        Map<Long, TenantResultResp> tenantVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(tenantVOList)){
            tenantVOMap = tenantVOList.stream().collect(Collectors.toMap(TenantResultResp::getId, item -> item));
        }
        Map<Long, MsgPublicTemplateWechatListResultResp> templateWechatMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(wechatListByCondition)) {
            templateWechatMap = wechatListByCondition.stream().collect(
              Collectors.toMap(MsgPublicTemplateWechatListResultResp::getId, item -> item));
        }
        List<MsgUserSceneDTO> sceneListVOList = new ArrayList<>();
        // mapping为空但是按条件查出的sceneList和templateWechatMap不为空，所以这里做个校验
        if (!CollectionUtils.isEmpty(mappingMap)){
            for (MsgScene msgScene : sceneList) {
                MsgUserSceneDTO sceneListDTO = MsgSceneConverter.msgScene2UserSceneListVO(msgScene, mappingMap, templateWechatMap ,tenantVOMap);
                sceneListVOList.add(sceneListDTO);
            }
        }
        PageInfo<MsgUserSceneDTO> pageInfo = new PageInfo<>();
        pageInfo.setPages((int) msgScenePage.getPages());
        pageInfo.setPageSize((int) page.getSize());
        pageInfo.setPageNum((int) page.getCurrent());
        pageInfo.setList(sceneListVOList);
        return pageInfo;
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateSceneByUser(MsgSceneUserWechatVO msgSceneUserWechatVO){
        if (ObjectUtils.isEmpty(msgSceneUserWechatVO)){
            throw new DefaultServiceException("参数错误，请联系管理员");
        }
        if (ObjectUtils.isEmpty(msgSceneUserWechatVO.getId())){
            throw new DefaultServiceException("参数错误，请联系管理员");
        }
        if (ObjectUtils.isEmpty(msgSceneUserWechatVO.getAvailableStatus())){
            throw new DefaultServiceException("参数错误，请联系管理员");
        }
        if (ObjectUtils.isEmpty(msgSceneUserWechatVO.getTenantId())){
            throw new DefaultServiceException("参数错误，请联系管理员");
        }
        msgSceneTenantMappingDao.updateByCondition(msgSceneUserWechatVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateScene(MsgSceneWechatVO msgSceneWechatVO){
        msgSceneDao.updateByCondition(msgSceneWechatVO);
    }

    /**
     * 按场景id查询微信模板id
     * @param templateIdQueryVO
     * @return
     */
    public String getThirdTemplateId(TemplateIdQueryVO templateIdQueryVO){
        MsgSceneTenantMapping mapping = msgSceneTenantMappingDao.getMappingByCondition(templateIdQueryVO);
        if(ObjectUtils.isEmpty(mapping)){
            return null;
        }
        return mapping.getThirdTemplateId();
    }

    public List<MsgSceneTenantMappingVO> listSceneTenantByTenantId(MsgSceneQueryDTO queryDTO,Long tenantId) {
        List<MsgSceneTenantMapping> mapping = msgSceneTenantMappingDao.listSceneTenantByTenantId(queryDTO,tenantId);
        return mapping.stream().map(e->MsgSceneConverter.sceneTenantMapping2VO(e)).collect(Collectors.toList());
    }
}
