package com.cosfo.oms.tenant.model.dto;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class CompanyAccountDTO implements Serializable {

    /**
     * 租户id
     */
    @NotNull(message = "品牌方不能为空")
    private Long tenantId;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商不能为空")
    private Long supplierTenantId;

    /**
     * 开户行
     */
    @NotBlank(message = "开户行不能为空")
    @Size(max = 255, message = "开户行过长")
    private String openingBank;

    /**
     * 户名
     */
    @NotBlank(message = "户名不能为空")
    @Size(max = 255, message = "户名过长")
    private String accountName;

    /**
     * 户号
     */
    @NotBlank(message = "户号不能为空")
    @Size(max = 255, message = "户号过长")
    private String accountNumber;

}
