package com.cosfo.oms.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账号表
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class TenantAccountDTO {

    private Long id;

    /**
     * 品牌方Id
     */
    private Long tenantId;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 登陆密码
     */
    private String loginPassword;
    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 头像
     */
    private String profilePicture;

    /**
     * 角色Id
     */
    private List<Long> roleIds;

    /**
     * 0有效1失效
     */
    private Integer status;

    /**
     * authBaseId
     */
    private Long authBaseId;

    /**
     * 操作时间
     */
    private LocalDateTime operatorTime;

    /**
     * 操作手机号
     */
    private String operatorPhone;

    /**
     * 系统来源,默认oms
     * @see net.xianmu.authentication.client.input.SystemOriginEnum
     */
    private Integer systemOrigin;

    /**
     * 供应商ids
     */
    private List<Long> supplierIds;

    /**
     * 操作人
     */
    private String updater;

    /**
     * email
     */
    private String email;
}

