package com.cosfo.oms.tenant.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/10
 */
@Data
@Getter
public class TenantVO {
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 商城名称
     */
    private String tenantName;
    /**
     * 管理员手机号
     */
    private String phone;
    /**
     * 租户联系人名称
     */
    private String contactName;
    /**
     * 租户类型：0-品牌方,1-鲜沐,2-帆台,3-外单
     */
    private Integer type;
    /**
     * 租户状态：0、禁用 1、启用
     */
    private Integer status;
    /**
     * 工商主体
     */
    private String companyName;
    /**
     * 信用代码/税号
     */
    private String creditCode;
    /**
     * 租户公司主键Id
     */
    private Long tenantCompanyId;
    /**
     * 大客户Id
     */
    private Long adminId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private String opUname;

    /**
     * 销售版本
     */
    private Integer saleVersion;

    /**
     * 销售版本名称
     */
    private String saleVersionName;

    /**
     * 到期时间
     */
    private LocalDate expireDate;

    /**
     * 是否过期
     */
    private Boolean isExpired;

    /**
     * 是否增购, true 增购
     */
    private Boolean isAddProcurement;

    /**
     * 省心订开关 0:关闭 1开启
     */
    private Integer saveWorrySwitch;
}
