package com.cosfo.oms.tenant.model.vo;

import com.cosfo.oms.tenant.model.dto.DeliveryFeeAreaRuleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/14
 */
@Data
public class TenantDeliveryFeeAreaRuleVO {
    /**
     * 分组Id
     */
    private Integer groupId;
    /**
     * 区域
     */
    private List<TenantDeliveryFeeAreaVO> tenantDeliveryFeeAreaVOList;
    /**
     * 运费金额
     */
    private BigDecimal defaultPrice;
    /**
     * 免配送费条件
     */
    private List<DeliveryFeeAreaRuleDTO> deliveryFeeAreaRuleDTOList;
}
