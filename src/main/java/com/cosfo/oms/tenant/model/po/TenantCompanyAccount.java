package com.cosfo.oms.tenant.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 供应商对品牌收款账户
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-21
 */
@Getter
@Setter
@TableName("tenant_company_account")
public class TenantCompanyAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键、自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应商id
     */
    private Long supplierTenantId;

    /**
     * 开户行
     */
    private String openingBank;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 户号
     */
    private String accountNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
