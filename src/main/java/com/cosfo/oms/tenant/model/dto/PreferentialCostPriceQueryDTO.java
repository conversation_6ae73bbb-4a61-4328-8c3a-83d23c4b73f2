package com.cosfo.oms.tenant.model.dto;

import com.cosfo.oms.model.dto.PageParamDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class PreferentialCostPriceQueryDTO extends PageParamDTO {
    /**
     * 商城Id
     */
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;
    /**
     * 货品名称
     */
    private String skuName;
    /**
     * 货品Id
     */
    private Long skuId;
}
