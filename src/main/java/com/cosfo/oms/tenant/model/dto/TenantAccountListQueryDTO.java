package com.cosfo.oms.tenant.model.dto;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class TenantAccountListQueryDTO extends BasePageInput {
    /**
     * id
     */
    private Long nickId;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 角色Id
     */
    private Long roleId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * authUserIds
     */
    private List<Long> authUserIds;
}
