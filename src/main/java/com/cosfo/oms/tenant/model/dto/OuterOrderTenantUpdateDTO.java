package com.cosfo.oms.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class OuterOrderTenantUpdateDTO {
    /**
     * 租户id
     */
    @NotNull(message = "tenantId 不能为空", groups = {ValidGroup.All.class})
    private Long tenantId;

    /**
     * 企业名称
     */
    @NotNull(message = "企业名称 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String companyName;

    /**
     * 信用代码
     */
    @NotNull(message = "信用代码 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String creditCode;

    /**
     * 省
     */
    @NotNull(message = "省 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String province;

    /**
     * 市
     */
    @NotNull(message = "市 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 联系地址
     */
    @NotNull(message = "联系地址 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String address;

    /**
     * 公司联系手机号
     */
    @NotNull(message = "公司联系手机号 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String companyPhone;
    /**
     * 联系电话-区号
     */
    private String companyAreaPhone;

    /**
     * 营业执照
     */
    @NotNull(message = "营业执照 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String businessLicense;


    /**
     * 租户联系人名称
     */
    @NotNull(message = "租户联系人 不能为空", groups = {ValidGroup.Update.class, ValidGroup.Insert.class})
    private String contactName;
}
