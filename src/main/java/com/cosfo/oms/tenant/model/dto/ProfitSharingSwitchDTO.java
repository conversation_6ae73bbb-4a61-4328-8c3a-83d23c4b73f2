package com.cosfo.oms.tenant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分账规则开关
 */
@Data
public class ProfitSharingSwitchDTO {
    /**
     * 分账开关0关 1开
     */
    @NotNull(message = "分账开关 不能为空")
    private Integer profitSharingSwitch;
    /**
     * 分账渠道 0 微信 1 汇付
     */
    @NotNull(message = "分账渠道 不能为空")
    private String onlinePayChannel;
    /**
     * 租户id
     */
    @NotNull(message = "租户id 不能为空")
    private Long tenantId;
}
