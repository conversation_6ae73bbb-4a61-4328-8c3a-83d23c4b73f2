package com.cosfo.oms.tenant.model.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/23
 */
@Data
public class RoleVO {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更改时间
     */
    private Date updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 最后操作人ID
     */
    private String lastUpdater;

    /**
     * 系统来源，0:admin 1:cosfomanage 2:cosfo
     */
    private Byte systemOrigin;

    /**
     * 0 默认 超级管理员 1
     */
    private Byte superAdmin;

    /**
     * 菜单列表
     */
    private List<MenuPurviewVO> menuPurviewVOList;

    private Integer roleCount;


    /**
     * 角色下菜单id列表
     */
    private List<Long> menuPurviewIds;
}
