package com.cosfo.oms.tenant.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户数据价值
 */
@Data
@TableName(value = "tenant_data_value")
public class TenantDataValue {
    /**
     * primary key
     */
    @TableId(value = "id")
    private Long id;

    /**
     * create time
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 租户Id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 授权人的外部用户系统用户id
     */
    @TableField(value = "authorizer_auth_user_id")
    private Long authorizerAuthUserId;

    /**
     * 报告上传oss地址
     */
    @TableField(value = "report_location")
    private String reportLocation;
}