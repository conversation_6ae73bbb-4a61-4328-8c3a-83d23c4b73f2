package com.cosfo.oms.tenant.service;



import com.cosfo.oms.tenant.model.dto.MenuPurviewDTO;
import com.cosfo.oms.tenant.model.vo.MenuPurviewVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuthMenuPurviewService {

    /**
     * 获取权限列表
     * @param systemEnum
     * @return
     */
    List<MenuPurviewVO> listMenuPurview(Integer systemEnum);

    /**
     * 获取菜单树-按系统
     * @param systemEnum
     * @return
     */
    List<MenuPurviewVO> listMenuPurviewTree(Integer systemEnum);


    /**
     * 获取当前用户菜单
     * @return
     */
    List<MenuPurviewVO> listUserMenuPurview();


    /**
     * 新增菜单
     * @param menuPurviewDTO
     * @return
     */
    Boolean addMenuPurview(MenuPurviewDTO menuPurviewDTO);

    /**
     * 编辑菜单
     * @param menuPurviewDTO
     * @return
     */
    Boolean updateMenuPurview(MenuPurviewDTO menuPurviewDTO);

    /**
     * 删除菜单
     * @param menuPurviewDTO
     * @return
     */
    Boolean delMenuPurview(MenuPurviewDTO menuPurviewDTO);

    /**
     * 排序
     * @param purviewDTOS
     * @return
     */
    Boolean sortMenuPurview(List<MenuPurviewDTO> purviewDTOS);
}
