package com.cosfo.oms.tenant.service;

import com.cosfo.oms.tenant.model.dto.TenantAuthServiceDTO;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/23
 */
public interface TenantPayConfigLogService {

    /**
     * 记录支付配置变更日志
     *
     * @param tenantAuthConnection
     * @param dto
     * @param tenantAccountId
     * @return
     */
    boolean saveTenantPayConfigLog(TenantAuthConnection tenantAuthConnection,
                                   TenantAuthServiceDTO dto,
                                   Long tenantAccountId);
}
