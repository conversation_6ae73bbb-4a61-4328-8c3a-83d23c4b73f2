package com.cosfo.oms.tenant.service.impl;

import cn.hutool.core.util.StrUtil;
import com.cosfo.common.util.TimeUtils;
import com.cosfo.oms.common.config.CommonConfig;
import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.common.constant.SmsConstants;
import com.cosfo.oms.common.constant.StringConstants;
import com.cosfo.oms.common.context.RedisKeyEnum;
import com.cosfo.oms.common.context.TenantAccountEnums;
import com.cosfo.oms.common.context.TenantEnums;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.PageInfoHelper;
import com.cosfo.oms.common.utils.RedisUtils;
import com.cosfo.oms.common.utils.StringUtils;
import com.cosfo.oms.facade.AuthUserFacade;
import com.cosfo.oms.facade.UserCenterTenantAccountFacade;
import com.cosfo.oms.facade.auth.AuthMockLoginFacade;
import com.cosfo.oms.facade.convert.tenant.TenantAccountMapper;
import com.cosfo.oms.facade.dto.AuthUseLoginDTO;
import com.cosfo.oms.facade.dto.tenant.TenantAccountCmdDTO;
import com.cosfo.oms.facade.dto.tenant.TenantAccountInputDTO;
import com.cosfo.oms.facade.dto.tenant.TenantInputQueryDTO;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.sms.SmsSender;
import com.cosfo.oms.sms.model.Sms;
import com.cosfo.oms.sms.model.SmsSenderFactory;
import com.cosfo.oms.tenant.convert.RoleConvert;
import com.cosfo.oms.tenant.model.dto.SendCodeDTO;
import com.cosfo.oms.tenant.model.dto.TenantAccountDTO;
import com.cosfo.oms.tenant.model.dto.TenantAccountListQueryDTO;
import com.cosfo.oms.tenant.model.dto.TenantAccountLoginDTO;
import com.cosfo.oms.tenant.model.po.TenantAccount;
import com.cosfo.oms.tenant.model.vo.RoleVO;
import com.cosfo.oms.tenant.model.vo.TenantAccountLoginVO;
import com.cosfo.oms.tenant.model.vo.TenantAccountVO;
import com.cosfo.oms.tenant.service.TenantAccountService;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.commons.lang3.RandomStringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Slf4j
@Service
public class TenantAccountServiceImpl implements TenantAccountService {
    @Resource
    private AuthUserFacade authUserFacade;
    @Resource
    private SmsSenderFactory smsSenderFactory;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private AuthMockLoginFacade authMockLoginFacade;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public TenantAccountLoginVO loginOld(TenantAccountLoginDTO tenantAccountLoginDTO) {
        // 校验用户密码是否正确
        Boolean checkPhonePassword = authUserFacade.checkPhonePasswordOld(tenantAccountLoginDTO.getPhone(), tenantAccountLoginDTO.getPassword());
        if (!checkPhonePassword) {
            throw new BizException(ResultDTOEnum.USER_OR_PASSWORD_WRONG.getMessage());
        }

        String token = authUserFacade.login(tenantAccountLoginDTO.getTenantId(), tenantAccountLoginDTO.getPhone(), tenantAccountLoginDTO.getPassword());
        TenantAccountLoginVO tenantAccountLoginVO = new TenantAccountLoginVO();
        tenantAccountLoginVO.setToken(token);
        return tenantAccountLoginVO;
    }

    @Override
    public TenantAccountLoginVO loginV2(TenantAccountLoginDTO tenantAccountLoginDTO) {
        if (!commonConfig.isPasswordLoginEnable()) {
            throw new BizException(ResultDTOEnum.PASSWORD_LOGIN_DISABLE.getMessage());
        }
        TenantAccountLoginVO tenantAccountLoginVO = new TenantAccountLoginVO();
        // 查询密码上次修改时间，比较时间差
        AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setPhone(tenantAccountLoginDTO.getPhone());
        AuthUserLastUpdatePwdTimeResp authUserLastUpdatePwdTimeResp = authUserFacade.queryLastUpdatePwdTime(authUserQueryInput);
        LocalDateTime lastUpdatePwdTime = Optional.ofNullable(authUserLastUpdatePwdTimeResp).map(AuthUserLastUpdatePwdTimeResp::getLastUpdatePwdTime).orElse(LocalDateTime.now());
        long days = Duration.between(lastUpdatePwdTime, LocalDateTime.now()).toDays();
        Integer changePasswordIntervalDays = commonConfig.getChangePasswordIntervalDays();
        tenantAccountLoginVO.setIntervalDay(changePasswordIntervalDays);
        Boolean needChangePassword = days > changePasswordIntervalDays;
        if (needChangePassword) {
            tenantAccountLoginVO.setNeedChangePassword(days > changePasswordIntervalDays);
            return tenantAccountLoginVO;
        }

        // 校验用户密码是否正确，确认是否到达重试次数，给出提示，完成
        AuthUseLoginDTO authUseLoginDTO = authUserFacade.checkPhonePassword(tenantAccountLoginDTO.getPhone(), tenantAccountLoginDTO.getPassword());
        fillTenantAccountLoginVO(tenantAccountLoginVO, authUseLoginDTO);
        if (!authUseLoginDTO.getPasswordSuccess() && Objects.nonNull(authUseLoginDTO.getErrorCount())) {
            return tenantAccountLoginVO;
        }
        if (!authUseLoginDTO.getPasswordSuccess()) {
            throw new BizException(ResultDTOEnum.USER_OR_PASSWORD_WRONG.getMessage());
        }

        String token = authUserFacade.login(tenantAccountLoginDTO.getTenantId(), tenantAccountLoginDTO.getPhone(), tenantAccountLoginDTO.getPassword());
        tenantAccountLoginVO.setToken(token);
        return tenantAccountLoginVO;
    }

    private void fillTenantAccountLoginVO(TenantAccountLoginVO tenantAccountLoginVO, AuthUseLoginDTO authUseLoginDTO) {
        tenantAccountLoginVO.setPasswordSuccess(authUseLoginDTO.getPasswordSuccess());
        tenantAccountLoginVO.setErrorCount(authUseLoginDTO.getErrorCount());
        tenantAccountLoginVO.setTotalCount(authUseLoginDTO.getTotalCount());
        tenantAccountLoginVO.setRemainCount(authUseLoginDTO.getRemainCount());
        tenantAccountLoginVO.setLockTime(authUseLoginDTO.getLockTime());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateUserInfo(TenantAccountDTO tenantAccountDTO, LoginContextInfoDTO loginContextInfoDTO) {

        if (Objects.isNull(tenantAccountDTO.getId())) {
            tenantAccountDTO.setAuthBaseId(loginContextInfoDTO.getAuthUserId());
            tenantAccountDTO.setPhone(loginContextInfoDTO.getPhone());
        } else if (!checkHavingSuperAdmin(loginContextInfoDTO.getAuthUserId())) {
            throw new BizException("不属于超级管理员账号，沒有修改权限");
        }

        if (Objects.isNull(tenantAccountDTO.getAuthBaseId())) {
            // 查询账号
            TenantAccountResultResp tenantAccount = userCenterTenantAccountFacade.getTenantAccountInfoById(tenantAccountDTO.getId());
            tenantAccountDTO.setAuthBaseId(tenantAccount.getAuthUserId());
            tenantAccountDTO.setPhone(tenantAccount.getPhone());
        }

        // 更新用户信息
        tenantAccountDTO.setOperatorTime(LocalDateTime.now());
        tenantAccountDTO.setOperatorPhone(loginContextInfoDTO.getPhone());
        tenantAccountDTO.setUpdater(loginContextInfoDTO.getUserName());

        TenantAccountCmdDTO tenantAccountCmdDTO = TenantAccountMapper.INSTANCE.accountDTOToCmd(tenantAccountDTO);
        // 查询品牌方租户
        List<Long> tenantIds = tenantInfoService.getTenantIdsByQuery(TenantInputQueryDTO.builder().type(TenantEnums.type.FANTAI.getCode()).build());
        tenantAccountCmdDTO.setTenantIds(tenantIds);
        tenantAccountCmdDTO.setSystemOrigin(SystemOriginEnum.COSFO_OMS.getType());

        userCenterTenantAccountFacade.updateTenantAccount(tenantAccountCmdDTO);
        // 更新auth服务用户信息
        return authUserFacade.updateUser(tenantAccountDTO);
    }

    private Boolean checkHavingSuperAdmin(Long authUserId) {
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(authUserId));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        if (authUserRoleDtoMap.containsKey(authUserId)) {
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(authUserId);
            List<AuthRole> roles = authUserRoleDto.getRoles();
            // 判断是否属于超级管理员，超级管理员角色不可变更
            boolean match = roles.stream().anyMatch(authRole -> TenantAccountEnums.Role.SUPER_ADMIN.getCode().equals(authRole.getSuperAdmin().intValue()));
            return match;
        }

        return Boolean.FALSE;
    }

    @Override
    public Boolean create(TenantAccountDTO tenantAccountDTO) {
        TenantAccountCmdDTO cmdDTO = TenantAccountMapper.INSTANCE.accountDTOToCmd(tenantAccountDTO);
        String password = createPassword(tenantAccountDTO.getPhone());
        cmdDTO.setLoginPassword(password);
        userCenterTenantAccountFacade.createTenantAccount(cmdDTO);
        return Boolean.TRUE;
    }

    @Override
    public TenantAccountVO getTenantAccountVO(Long authUserId) {
        // 查询用户信息
        TenantAccountResultResp tenantAccountInfo = userCenterTenantAccountFacade.getTenantAccountInfo(authUserId);
        TenantAccountVO tenantAccountVO = TenantAccountMapper.INSTANCE.dtoToVO(tenantAccountInfo, null);
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(authUserId));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        tenantAccountVO = getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);
        Integer changePasswordIntervalDays = commonConfig.getChangePasswordIntervalDays();
        // 查询密码上次修改时间，比较时间差
        Boolean needChangePassword = queryUserNeedChangePassword(tenantAccountVO.getPhone(), changePasswordIntervalDays);
        tenantAccountVO.setIntervalDay(changePasswordIntervalDays);
        tenantAccountVO.setNeedChangePassword(needChangePassword);

        return tenantAccountVO;
    }

    /**
     * 查询用户是否需要修改密码
     *
     * @param phone
     * @param changePasswordIntervalDays
     * @return
     */
    private Boolean queryUserNeedChangePassword(String phone, Integer changePasswordIntervalDays) {
        AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setPhone(phone);
        AuthUserLastUpdatePwdTimeResp authUserLastUpdatePwdTimeResp = authUserFacade.queryLastUpdatePwdTime(authUserQueryInput);
        LocalDateTime lastUpdatePwdTime = Optional.ofNullable(authUserLastUpdatePwdTimeResp).map(AuthUserLastUpdatePwdTimeResp::getLastUpdatePwdTime).orElse(LocalDateTime.now());
        long days = Duration.between(lastUpdatePwdTime, LocalDateTime.now()).toDays();
        return days > changePasswordIntervalDays;
    }


    @Override
    public PageInfo<TenantAccountVO> page(TenantAccountListQueryDTO tenantAccountListQueryDTO, LoginContextInfoDTO loginContextInfoDTO) {
        Long tenantId = loginContextInfoDTO.getTenantId();
        TenantAccountInputDTO accountInputDTO = TenantAccountMapper.INSTANCE.queryToInputDTO(tenantAccountListQueryDTO);
        accountInputDTO.setTenantId(tenantId);
        if (Objects.nonNull(tenantAccountListQueryDTO.getRoleId())) {
            List<Long> authUserIds = authUserFacade.getUserIdListByRoleId(tenantAccountListQueryDTO.getRoleId());
            if (CollectionUtils.isEmpty(authUserIds)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), tenantAccountListQueryDTO.getPageSize());
            }

            accountInputDTO.setAuthUserIds(authUserIds);
        }
        PageInfo<TenantAccountResultResp> tenantAccountInfoPage = userCenterTenantAccountFacade.getTenantAccountInfoPage(accountInputDTO);

        if (CollectionUtils.isEmpty(tenantAccountInfoPage.getList())) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), tenantAccountListQueryDTO.getPageSize());
        }
        List<Long> authUserIds = tenantAccountInfoPage.getList().stream().map(TenantAccountResultResp::getAuthUserId).collect(Collectors.toList());
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(authUserIds);
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        return TenantAccountMapper.INSTANCE.dtoPageToVoPage(tenantAccountInfoPage, authUserRoleDtoMap);
    }

    @Override
    public void remove(Long tenantAccountId, LoginContextInfoDTO loginContextInfoDTO) {
        if(!checkHavingSuperAdmin(loginContextInfoDTO.getAuthUserId())){
            throw new BizException("您不属于超级管理员，不可删除该用户");
        }
        TenantAccountResultResp tenantAccountInfoDTO = userCenterTenantAccountFacade.getTenantAccountInfoById(tenantAccountId);
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(tenantAccountInfoDTO.getAuthUserId()));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        if(authUserRoleDtoMap.containsKey(tenantAccountInfoDTO.getAuthUserId())){
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(tenantAccountInfoDTO.getAuthUserId());
            List<AuthRole> roles = authUserRoleDto.getRoles();
            // 判断是否属于超级管理员，超级管理员角色不可变更
            boolean match = roles.stream().anyMatch(authRole -> TenantAccountEnums.Role.SUPER_ADMIN.getCode().equals(authRole.getSuperAdmin().intValue()));
            if(match){
                throw new BizException("该用户属于超级管理员，不可删除该用户");
            }
        }
        userCenterTenantAccountFacade.delTenantAccount(tenantAccountId, loginContextInfoDTO.getAuthUserId());
        TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
        tenantAccountDTO.setAuthBaseId(tenantAccountInfoDTO.getAuthUserId());
        tenantAccountDTO.setStatus(TenantAccountEnums.status.FAILURE.getCode());
        authUserFacade.updateUser(tenantAccountDTO);
    }

    @Override
    public Boolean sendCode(String phone) {
        RLock lock = redissonClient.getLock(RedisKeyEnum.C00001.join(phone));
        if (!lock.tryLock()) {
            throw new BizException(phone + "正在发送验证码，请稍后再试");
        }
        try {
            String cacheKey = RedisKeyEnum.C00002.join(phone);
            if (Objects.nonNull(redisTemplate.opsForValue().get(cacheKey))) {
                throw new BizException(phone + "近一分钟已发送验证码，请稍后再试");
            }
            String code = String.valueOf(SmsSender.buildRandom(6));
            Sms sms = new Sms();
            sms.setPhone(phone);
            sms.setSceneId(SmsConstants.SCENE_ID);
            sms.setArgs(Arrays.asList(code));

            // 发送验证码
            boolean success = smsSenderFactory.getSmsSender().sendSms(sms);
            if (!success) {
                throw new BizException(ResultDTOEnum.SEND_CODE_FAIL.getMessage());
            }

            // 缓存
            String key = SmsConstants.CODE_PREFIX + phone;
            redisUtils.set(key, code, SmsConstants.EXPIRATION_TIME);
            // 设置发送标记
            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstants.ONE), commonConfig.getSmsSendIntervalTime(), TimeUnit.MILLISECONDS);

            log.info("phone：{} code：{}", phone, code);
        } finally {
            lock.unlock();
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean examineCode(SendCodeDTO sendCodeDTO) {
        String phone = sendCodeDTO.getPhone();
        String examineCode = sendCodeDTO.getCode();
        // 无缓存
        String key = SmsConstants.CODE_PREFIX + phone;
        Object codeObj = redisUtils.get(key);
        if (Objects.isNull(codeObj)) {
            throw new BizException("验证码已失效，请重新获取验证码");
        }
        // 验证码错误
        if (!Objects.equals(codeObj.toString(), examineCode)) {
            throw new BizException("验证码不正确");
        }

        // 更改密码
        if (!StringUtils.isEmpty(sendCodeDTO.getLoginPassword())) {
            TenantAccountDTO tenantAccountDTO = new TenantAccountDTO();
            tenantAccountDTO.setPhone(phone);
            tenantAccountDTO.setLoginPassword(sendCodeDTO.getLoginPassword());
            updatePassword(tenantAccountDTO);
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean updatePassword(TenantAccountDTO tenantAccountDTO) {
        boolean success = StringUtils.checkAccountPassword(tenantAccountDTO.getLoginPassword());
        if (!success) {
            throw new BizException("密码格式不正确(8-20字符，需包含大小写字母和数字)");
        }

        // 查询品牌方租户
        TenantInputQueryDTO queryDTO = TenantInputQueryDTO.builder().type(TenantEnums.type.FANTAI.getCode()).build();
        List<Long> tenantIds = tenantInfoService.getTenantIdsByQuery(queryDTO);
        // 校验是否多租户
        List<TenantAccountResultResp> tenantAccounts = userCenterTenantAccountFacade.getTenantAccountByTenantIdsAndPhone(tenantIds, tenantAccountDTO.getPhone());
        if (CollectionUtils.isEmpty(tenantAccounts)) {
            throw new BizException(ResultDTOEnum.ACCOUNT_FAILURE.getMessage());
        }

        Long authUserId = tenantAccounts.get(NumberConstants.ZERO).getAuthUserId();
        tenantAccountDTO.setAuthBaseId(authUserId);
        authUserFacade.updateUser(tenantAccountDTO);
        return Boolean.TRUE;
    }

    @Override
    public TenantAccountVO queryAccountInfo(Long tenantId, Long userId) {
        // 查询账号信息
        TenantAccountResultResp tenantAccount = userCenterTenantAccountFacade.getTenantAccountInfoById(userId);
        if (Objects.isNull(tenantAccount)) {
            throw new BizException("账号不存在");
        }

        TenantAccountVO tenantAccountVO = TenantAccountMapper.INSTANCE.dtoToVO(tenantAccount, null);
        // 查询绑定的角色
        List<AuthUserRoleDto> authUserRoleDtos = authUserFacade.getUserRoleByUserIds(Arrays.asList(tenantAccount.getAuthUserId()));
        Map<Long, AuthUserRoleDto> authUserRoleDtoMap = authUserRoleDtos.stream().collect(Collectors.toMap(AuthUserRoleDto::getId, item -> item));
        return getTenantAccountVO(tenantAccountVO, authUserRoleDtoMap);
    }

    @Override
    public Map<Long, TenantAccount> queryAccountMap(Set<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }
        List<TenantAccountResultResp> tenantAccounts = userCenterTenantAccountFacade.getTenantAccountByUserIds(Lists.newArrayList(userIds));
        return tenantAccounts.stream().collect(Collectors.toMap(TenantAccountResultResp::getAuthUserId, TenantAccountMapper.INSTANCE::dtoToAccount));
    }

    private TenantAccountVO getTenantAccountVO(TenantAccountVO tenantAccountVO, Map<Long, AuthUserRoleDto> authUserRoleDtoMap) {
        if (authUserRoleDtoMap.containsKey(tenantAccountVO.getAuthUserId())) {
            AuthUserRoleDto authUserRoleDto = authUserRoleDtoMap.get(tenantAccountVO.getAuthUserId());
            List<AuthRole> roles = authUserRoleDto.getRoles();
            List<RoleVO> roleVOS = RoleConvert.convertToRoleVOList(roles);
            tenantAccountVO.setRoleVos(roleVOS);
        }

        return tenantAccountVO;
    }

    @Override
    public String createPassword(String phone) {
        // 获取年份
        String year = TimeUtils.changeDate2String(new Date(), TimeUtils.FORMAT_YEAR_ONLY);
        // 获取手机后四位
        String substring = RandomStringUtils.randomNumeric(4);
        if(StrUtil.isNotBlank(phone)) {
            substring = phone.substring(phone.length() - NumberConstants.FOUR);
        }
        StringBuffer stringBuffer = new StringBuffer(year).append(StringConstants.CHARACTER).append(substring);
        // 生成随机四位大小写字符串
        String randomStr = RandomStringUtils.randomAlphabetic(4);
        while (!StringUtils.checkAccountPassword(stringBuffer.toString() + randomStr)) {
            randomStr = RandomStringUtils.randomAlphabetic(4);
        }
        stringBuffer.append(randomStr);
        return stringBuffer.toString();
    }

    @Override
    public String loginSuperAccount(Long targetTenantId, Long authUserId) {
        AuthLoginDto authLoginDto = authMockLoginFacade.mockLogin(targetTenantId, authUserId, commonConfig.getSuperTokenExpireTime());
        if (authLoginDto == null) {
            log.warn("超级账号token获取失败, targetTenantId: {}, token: {}", targetTenantId, authUserId);
            throw new BizException("超级账号token获取失败");
        }
        return authLoginDto.getToken();
    }
}
