package com.cosfo.oms.tenant.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.oms.common.constant.Constants;
import com.cosfo.oms.common.context.PrivilegesConfigTypeEnum;
import com.cosfo.oms.common.context.tenant.TenantConfigEnum;
import com.cosfo.oms.facade.auth.AuthTenantPrivilegesFacade;
import com.cosfo.oms.facade.input.TenantPrivilegesSyncInput;
import com.cosfo.oms.tenant.convert.TenantPrivilegesConfigConverter;
import com.cosfo.oms.tenant.dao.TenantPrivilegesConfigDao;
import com.cosfo.oms.tenant.mapper.TenantCommonConfigMapper;
import com.cosfo.oms.tenant.model.dto.FunctionSetDTO;
import com.cosfo.oms.tenant.model.dto.PrivilegesConfigInfoDTO;
import com.cosfo.oms.tenant.model.dto.PrivilegesFunctionSetDTO;
import com.cosfo.oms.tenant.model.dto.TenantPrivilegesConfigDTO;
import com.cosfo.oms.tenant.model.po.TenantCommonConfig;
import com.cosfo.oms.tenant.model.po.TenantPrivilegesConfig;
import com.cosfo.oms.tenant.model.vo.TenantPrivilegesConfigDetailVO;
import com.cosfo.oms.tenant.model.vo.TenantPrivilegesConfigVO;
import com.cosfo.oms.tenant.model.vo.TenantPrivilegesSimpleConfigVO;
import com.cosfo.oms.tenant.service.TenantFunctionSetService;
import com.cosfo.oms.tenant.service.TenantLogService;
import com.cosfo.oms.tenant.service.TenantPrivilegesConfigService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantPrivilegesConfigServiceImpl implements TenantPrivilegesConfigService {

    @Resource
    private TenantFunctionSetService tenantFunctionSetService;
    @Resource
    private TenantPrivilegesConfigDao tenantPrivilegesConfigDao;
    @Resource
    private AuthTenantPrivilegesFacade authTenantPrivilegesFacade;
    @Resource
    private TenantCommonConfigMapper tenantCommonConfigMapper;
    @Resource
    private TenantLogService tenantLogService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateTenantPrivileges(TenantPrivilegesConfigDTO tenantPrivilegesConfigDTO, Long tenantId) {

        // 更新客户经理信息
        updateCustomerManagerInfo(tenantPrivilegesConfigDTO, tenantId);

        //获取当前权益
        List<TenantPrivilegesConfig> privilegesConfigDetail = tenantPrivilegesConfigDao.getPrivilegesConfig(tenantId);


        if (CollectionUtils.isEmpty(privilegesConfigDetail)) {
            // 新增逻辑
            handleNewConfigPrivileges(tenantPrivilegesConfigDTO, tenantId);
            return true;
        }
        Map<Long, FunctionSetDTO> funcMap = new HashMap<>();
        List<Long> funcSetIds = privilegesConfigDetail.stream().map(TenantPrivilegesConfig::getFunctionSetId).collect(Collectors.toList());
        funcMap = tenantFunctionSetService.mapByIds(funcSetIds);

        // 处理销售版本更新
        TenantPrivilegesConfig saleVersionUpdate = handleSaleVersion(tenantPrivilegesConfigDTO, tenantId, privilegesConfigDetail, funcMap);
        // 处理权益
        handlePrivilegesConfig(tenantPrivilegesConfigDTO, tenantId, saleVersionUpdate, privilegesConfigDetail);
        syncAuthPurview(tenantId);
        cacheTenantPrivilegesConfig(tenantId, saleVersionUpdate);
        return true;
    }

    private void handleNewConfigPrivileges(TenantPrivilegesConfigDTO tenantPrivilegesConfigDTO, Long tenantId) {

        TenantPrivilegesConfig saleVersionUpdate = new TenantPrivilegesConfig();
        saleVersionUpdate.setTenantId(tenantId);
        saleVersionUpdate.setFunctionSetId(tenantFunctionSetService.getFunctionSetBySaleVersion(tenantPrivilegesConfigDTO.getSaleVersion()).getId());
        saleVersionUpdate.setExpirationTime(tenantPrivilegesConfigDTO.getExpireDate());
        saleVersionUpdate.setEffectiveTime(LocalDate.now());
        saleVersionUpdate.setConfigType(PrivilegesConfigTypeEnum.SALE_VERSION.getCode());
        tenantLogService.logVersionUpdate(tenantId, null, tenantPrivilegesConfigDTO.getSaleVersion());
        tenantPrivilegesConfigDao.save(saleVersionUpdate);

        List<TenantPrivilegesConfig> addList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tenantPrivilegesConfigDTO.getFunctionSetList())) {
            for (PrivilegesFunctionSetDTO functionSetDTO : tenantPrivilegesConfigDTO.getFunctionSetList()) {
                TenantPrivilegesConfig add = new TenantPrivilegesConfig();
                add.setTenantId(tenantId);
                add.setFunctionSetId(functionSetDTO.getFunctionSetId());
                add.setExpirationTime(functionSetDTO.getExpireDate());
                add.setEffectiveTime(LocalDate.now());
                add.setConfigType(PrivilegesConfigTypeEnum.ADD_FUNCTION.getCode());
                add.setFlagType(functionSetDTO.getFlagType());
                addList.add(add);
                tenantLogService.logPrivileges(tenantId, Collections.emptyList(), tenantPrivilegesConfigDTO.getFunctionSetList());
            }
            tenantPrivilegesConfigDao.saveBatch(addList);
        }
        syncAuthPurview(tenantId);
        cacheTenantPrivilegesConfig(tenantId, saleVersionUpdate);

    }

    private void handlePrivilegesConfig(TenantPrivilegesConfigDTO tenantPrivilegesConfigDTO, Long tenantId, TenantPrivilegesConfig saleVersionUpdate, List<TenantPrivilegesConfig> privilegesConfigDetail) {
        Map<Long, PrivilegesFunctionSetDTO> editMap = new HashedMap();
        List<TenantPrivilegesConfig> updateList = new ArrayList<>();
        List<TenantPrivilegesConfig> deleteList = new ArrayList<>();
        List<TenantPrivilegesConfig> addList = new ArrayList<>();
        updateList.add(saleVersionUpdate);
        if (!CollectionUtils.isEmpty(tenantPrivilegesConfigDTO.getFunctionSetList())) {
            editMap = tenantPrivilegesConfigDTO.getFunctionSetList().stream().collect(Collectors.toMap(PrivilegesFunctionSetDTO::getFunctionSetId, set -> set));
            List<Long> funcSetIdsUpdate = tenantPrivilegesConfigDTO.getFunctionSetList().stream().map(PrivilegesFunctionSetDTO::getFunctionSetId).collect(Collectors.toList());
            List<Long> funcSetIdsOld = privilegesConfigDetail.stream().filter(config -> config.getConfigType() == 1).map(TenantPrivilegesConfig::getFunctionSetId).collect(Collectors.toList());
            // 取交集，更新数据
            List<Long> updatePrivilegesIds = funcSetIdsOld.stream().filter(funcSetIdsUpdate::contains).collect(Collectors.toList());
            // 取差集，删除数据
            List<Long> removePrivilegesIds = funcSetIdsOld.stream().filter(item -> !funcSetIdsUpdate.contains(item)).collect(Collectors.toList());
            // 取差集，新增数据
            List<Long> newPrivilegesIds = funcSetIdsUpdate.stream().filter(item -> !funcSetIdsOld.contains(item)).collect(Collectors.toList());

            for (TenantPrivilegesConfig config : privilegesConfigDetail) {
                if (updatePrivilegesIds.contains(config.getFunctionSetId())) {
                    TenantPrivilegesConfig update = new TenantPrivilegesConfig();
                    update.setId(config.getId());
                    update.setExpirationTime(editMap.get(config.getFunctionSetId()).getExpireDate());
                    update.setConfigType(PrivilegesConfigTypeEnum.ADD_FUNCTION.getCode());
                    update.setFlagType(editMap.get(config.getFunctionSetId()).getFlagType());
                    updateList.add(update);
                } else if (removePrivilegesIds.contains(config.getFunctionSetId())) {
                    deleteList.add(config);
                }
            }
            for (Long functionSetId : newPrivilegesIds) {
                TenantPrivilegesConfig add = new TenantPrivilegesConfig();
                add.setTenantId(tenantId);
                add.setFunctionSetId(functionSetId);
                add.setExpirationTime(editMap.get(functionSetId).getExpireDate());
                add.setEffectiveTime(LocalDate.now());
                add.setConfigType(PrivilegesConfigTypeEnum.ADD_FUNCTION.getCode());
                add.setFlagType(editMap.get(functionSetId).getFlagType());
                addList.add(add);
            }

        } else {
            //删除所有
            deleteList.addAll(privilegesConfigDetail.stream().filter(config -> Objects.equals(config.getConfigType(), PrivilegesConfigTypeEnum.ADD_FUNCTION.getCode())).collect(Collectors.toList()));
        }
        tenantLogService.logPrivileges(tenantId, privilegesConfigDetail, tenantPrivilegesConfigDTO.getFunctionSetList());

        if (!CollectionUtils.isEmpty(updateList)) {
            tenantPrivilegesConfigDao.updateBatchById(updateList);
        }
        if (!CollectionUtils.isEmpty(deleteList)) {
            tenantPrivilegesConfigDao.removeByIds(deleteList.stream().map(TenantPrivilegesConfig::getId).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(addList)) {
            tenantPrivilegesConfigDao.saveBatch(addList);
        }
    }

    private TenantPrivilegesConfig handleSaleVersion(TenantPrivilegesConfigDTO tenantPrivilegesConfigDTO, Long tenantId, List<TenantPrivilegesConfig> privilegesConfigDetail, Map<Long, FunctionSetDTO> funcMap) {
        TenantPrivilegesConfig saleVersionConfig = privilegesConfigDetail.stream()
                .filter(config -> config.getConfigType() == PrivilegesConfigTypeEnum.SALE_VERSION.getCode())
                .findAny()
                .orElse(null);
        if (saleVersionConfig == null) {
            throw new BizException("当前租户未配置版本信息");
        }
        //获取当前版本
        Integer saleVersion = funcMap.get(saleVersionConfig.getFunctionSetId()).getSaleVersion();

        TenantPrivilegesConfig saleVersionUpdate = new TenantPrivilegesConfig();
        //判断版本是否变更
        if (tenantPrivilegesConfigDTO.getSaleVersion().equals(saleVersion)) {
            //版本未变更
            //判断是否有增购
            saleVersionUpdate.setId(saleVersionConfig.getId());
            saleVersionUpdate.setExpirationTime(tenantPrivilegesConfigDTO.getExpireDate());
        } else {
            //版本变更
            saleVersionUpdate.setId(saleVersionConfig.getId());
            saleVersionUpdate.setExpirationTime(tenantPrivilegesConfigDTO.getExpireDate());
            saleVersionUpdate.setFunctionSetId(tenantFunctionSetService.getFunctionSetBySaleVersion(tenantPrivilegesConfigDTO.getSaleVersion()).getId());
            saleVersionUpdate.setEffectiveTime(LocalDate.now());
            tenantLogService.logVersionUpdate(tenantId, saleVersion, tenantPrivilegesConfigDTO.getSaleVersion());
        }
        // 时间不等
        if (!Objects.equals(saleVersionConfig.getExpirationTime(), tenantPrivilegesConfigDTO.getExpireDate())) {
            tenantLogService.logVersionExpireDateUpdate(tenantId, saleVersionConfig.getExpirationTime(), tenantPrivilegesConfigDTO.getExpireDate());
        }
        return saleVersionUpdate;
    }

    private void updateCustomerManagerInfo(TenantPrivilegesConfigDTO tenantPrivilegesConfigDTO, Long tenantId) {
        TenantCommonConfig customerManageConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.TENANT_CUSTOMER_MANAGER.getConfigKey());
        if (customerManageConfig != null) {
            TenantCommonConfig customerManageConfigUpdate = new TenantCommonConfig();
            customerManageConfigUpdate.setId(customerManageConfig.getId());
            customerManageConfigUpdate.setConfigValue(tenantPrivilegesConfigDTO.getCustomerManager());
            tenantCommonConfigMapper.updateConfigValueById(customerManageConfigUpdate);
        } else {
            TenantCommonConfig newCustomerManager = new TenantCommonConfig();
            newCustomerManager.setConfigKey(TenantConfigEnum.TENANT_CUSTOMER_MANAGER.getConfigKey());
            newCustomerManager.setConfigDesc(TenantConfigEnum.TENANT_CUSTOMER_MANAGER.getConfigDesc());
            newCustomerManager.setConfigValue(tenantPrivilegesConfigDTO.getCustomerManager());
            newCustomerManager.setTenantId(tenantId);
            tenantCommonConfigMapper.insertSelective(newCustomerManager);
        }
    }


    @Override
    public void syncAuthPurview(Long tenantId) {
        log.info("开始同步租户{}权益", tenantId);
        List<TenantPrivilegesConfig> privilegesConfigDetail = tenantPrivilegesConfigDao.getPrivilegesConfig(tenantId);
        Map<Integer, LocalDate> purviewMap = handleMenu(privilegesConfigDetail);
        TenantPrivilegesSyncInput syncInput = new TenantPrivilegesSyncInput();
        syncInput.setTenantId(tenantId);
        syncInput.setPurviewMap(purviewMap);
        authTenantPrivilegesFacade.addTenantPrivileges(syncInput);
        log.info("同步租户{}权益结束", tenantId);
    }

    @Override
    public boolean saveTenantPrivileges(List<TenantPrivilegesConfig> configDTOS) {
        return tenantPrivilegesConfigDao.savePrivilegesConfigs(configDTOS);
    }

    @Override
    public Map<Integer, LocalDate> handleMenu(List<TenantPrivilegesConfig> privilegesConfigs) {
        List<Long> funcSetIds = privilegesConfigs.stream().map(TenantPrivilegesConfig::getFunctionSetId).collect(Collectors.toList());
        Map<Long, FunctionSetDTO> functionSetMap = tenantFunctionSetService.mapByIds(funcSetIds);
//        Optional<TenantPrivilegesConfig> saleVersionOptional = privilegesConfigs.stream().filter(config -> Objects.equals(config.getConfigType(), PrivilegesConfigTypeEnum.SALE_VERSION.getCode())).findAny();
//        LocalDate expirationTime = saleVersionOptional.get().getExpirationTime();
        Map<Integer, LocalDate> purviewMap = new HashMap<>();
        privilegesConfigs.stream().filter(config -> Objects.equals(config.getConfigType(), PrivilegesConfigTypeEnum.ADD_FUNCTION.getCode())).forEach(config -> {
            FunctionSetDTO functionSet = functionSetMap.get(config.getFunctionSetId());
            functionSet.getPurviewIds().forEach(purviewId -> {
                LocalDate localDate = purviewMap.get(purviewId);
                if (localDate == null) {
                    purviewMap.put(purviewId, config.getExpirationTime());
                } else {
                    if (localDate.isBefore(config.getExpirationTime())) {
                        purviewMap.put(purviewId, config.getExpirationTime());
                    }
                }
            });
        });
        privilegesConfigs.stream().filter(config -> Objects.equals(config.getConfigType(), PrivilegesConfigTypeEnum.SALE_VERSION.getCode())).forEach(config -> {
            FunctionSetDTO functionSet = functionSetMap.get(config.getFunctionSetId());
            functionSet.getPurviewIds().forEach(purviewId -> {
                purviewMap.put(purviewId, config.getExpirationTime());
            });
        });
        return purviewMap;
    }

    @Override
    public boolean cacheTenantPrivilegesConfig(Long tenantId, TenantPrivilegesConfig tenantPrivilegesConfig) {
        stringRedisTemplate.opsForValue()
                .set(Constants.TENANT_PRIVILEGES + tenantId,
                        JSON.toJSONString(PrivilegesConfigInfoDTO.builder()
                                .expireDate(tenantPrivilegesConfig.getExpirationTime())
                                .build()));
        return true;
    }

    @Async(value = "privilegesSyncTaskExecutor")
    @Override
    public void syncWithFuncChange(Long funcId) {
        Set<Long> tenantByFunctionSet = tenantPrivilegesConfigDao.getTenantByFunctionSet(funcId);
        log.info("功能集{}发生变更, 需要同步租户{}权益", funcId, tenantByFunctionSet);
        if (CollectionUtils.isEmpty(tenantByFunctionSet)) {
            return;
        }
        for (Long tenantId : tenantByFunctionSet) {
            syncAuthPurview(tenantId);
        }
    }

    @Override
    public List<Long> getTenantIdsByExpireDate() {
        return tenantPrivilegesConfigDao.getTenantIdsByExpireDate();
    }

    @Override
    public Map<Long, TenantPrivilegesConfigDTO> getTenantPrivilegesConfigMap(List<Long> tenantIds) {
        List<TenantPrivilegesConfig> privilegesConfigList = tenantPrivilegesConfigDao.getTenantPrivilegesConfigByTenantIds(tenantIds, PrivilegesConfigTypeEnum.SALE_VERSION);
        if (CollectionUtils.isEmpty(privilegesConfigList)) {
            return Collections.emptyMap();
        }
        return privilegesConfigList.stream().collect(Collectors.toMap(TenantPrivilegesConfig::getTenantId, TenantPrivilegesConfigConverter.INSTANCE::toTenantPrivilegesConfigDTO));
    }

    @Override
    public TenantPrivilegesConfigDetailVO getTenantPrivilegesDetail(Long tenantId) {
        List<TenantPrivilegesConfig> privilegesConfigDetail = tenantPrivilegesConfigDao.getPrivilegesConfig(tenantId);
        // 获取当前版本
        TenantPrivilegesConfig saleVersionConfig = privilegesConfigDetail.stream().filter(config -> config.getConfigType() == 0).findAny().orElse(null);
        if (saleVersionConfig == null) {
            return null;
        }
        TenantCommonConfig customerManageConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.TENANT_CUSTOMER_MANAGER.getConfigKey());
        Map<Long, FunctionSetDTO> funcMap = getFuncionSetMap(privilegesConfigDetail);
        TenantPrivilegesConfigDetailVO tenantPrivilegesConfigDTO = new TenantPrivilegesConfigDetailVO();
        tenantPrivilegesConfigDTO.setSaleVersion(funcMap.get(saleVersionConfig.getFunctionSetId()).getSaleVersion());
        tenantPrivilegesConfigDTO.setExpireDate(saleVersionConfig.getExpirationTime());
        if (customerManageConfig != null) {
            tenantPrivilegesConfigDTO.setCustomerManager(customerManageConfig.getConfigValue());
        }
        List<TenantPrivilegesConfigVO> collect = privilegesConfigDetail.stream().filter(config -> config.getConfigType() == 1).map(config -> {
            TenantPrivilegesConfigVO privilegesFunctionSetDTO = new TenantPrivilegesConfigVO();
            FunctionSetDTO functionSetDTO = funcMap.get(config.getFunctionSetId());
            privilegesFunctionSetDTO.setFunctionSetId(config.getFunctionSetId());
            privilegesFunctionSetDTO.setName(functionSetDTO.getName());
            privilegesFunctionSetDTO.setDesc(functionSetDTO.getDesc());
            privilegesFunctionSetDTO.setExpireDate(config.getExpirationTime());
            privilegesFunctionSetDTO.setFlagType(config.getFlagType());
            return privilegesFunctionSetDTO;
        }).collect(Collectors.toList());

        tenantPrivilegesConfigDTO.setPrivilegesConfigList(collect);


        TenantCommonConfig currencyConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.CURRENCY.getConfigKey());
        if(currencyConfig != null){
            tenantPrivilegesConfigDTO.setCurrency(NumberUtils.toInt(currencyConfig.getConfigValue(), 1));
        } else {
            tenantPrivilegesConfigDTO.setCurrency(NumberUtils.toInt(TenantConfigEnum.CURRENCY.getDefaultValue()));
        }

        return tenantPrivilegesConfigDTO;
    }

    @Override
    public Map<Long, TenantPrivilegesConfigDetailVO> getTenantPrivilegesDetailBatch(List<Long> tenantIds) {
        Map<Long, TenantPrivilegesConfigDetailVO> map = new HashedMap(tenantIds.size());
        for (Long tenantId : tenantIds) {
            map.put(tenantId, getTenantPrivilegesDetail(tenantId));
        }
        return map;
    }

    @Override
    public TenantPrivilegesSimpleConfigVO getTenantPrivileges(Long tenantId) {
        List<TenantPrivilegesConfig> privilegesConfigDetail = tenantPrivilegesConfigDao.getPrivilegesConfig(tenantId);
        // 获取当前版本
        TenantPrivilegesConfig saleVersionConfig = privilegesConfigDetail.stream()
                .filter(config -> Objects.equals(config.getConfigType(), PrivilegesConfigTypeEnum.SALE_VERSION.getCode()))
                .findAny().orElse(null);
        if (saleVersionConfig == null) {
            return null;
        }
        // 获取客户经理
        TenantCommonConfig customerManageConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.TENANT_CUSTOMER_MANAGER.getConfigKey());
        Map<Long, FunctionSetDTO> funcMap = getFuncionSetMap(privilegesConfigDetail);
        TenantPrivilegesSimpleConfigVO simpleConfigVO = new TenantPrivilegesSimpleConfigVO();
        simpleConfigVO.setSaleVersion(funcMap.get(saleVersionConfig.getFunctionSetId()).getSaleVersion());
        simpleConfigVO.setExpireDate(saleVersionConfig.getExpirationTime());
        simpleConfigVO.setFunctionCnt(privilegesConfigDetail.size() - 1);
        simpleConfigVO.setExpireFunctionCnt((int) privilegesConfigDetail.stream()
                .filter(config -> Objects.equals(config.getConfigType(), PrivilegesConfigTypeEnum.ADD_FUNCTION.getCode())
                        && LocalDate.now().isAfter(config.getExpirationTime()))
                .count());
        if (customerManageConfig != null) {
            simpleConfigVO.setCustomerManager(customerManageConfig.getConfigValue());
        }

        TenantCommonConfig currencyConfig = tenantCommonConfigMapper.selectByTenantIdAndConfigKey(tenantId, TenantConfigEnum.CURRENCY.getConfigKey());
        if(currencyConfig != null){
            simpleConfigVO.setCurrency(NumberUtils.toInt(currencyConfig.getConfigValue(), 1));
        } else {
            simpleConfigVO.setCurrency(NumberUtils.toInt(TenantConfigEnum.CURRENCY.getDefaultValue()));
        }

        return simpleConfigVO;
    }

    private Map<Long, FunctionSetDTO> getFuncionSetMap(List<TenantPrivilegesConfig> privilegesConfigDetail) {
        List<Long> funcSetIds = privilegesConfigDetail.stream().map(TenantPrivilegesConfig::getFunctionSetId).collect(Collectors.toList());
        List<FunctionSetDTO> functionSetDTOS = tenantFunctionSetService.listByIds(funcSetIds);
        Map<Long, FunctionSetDTO> funcMap = functionSetDTOS.stream().collect(Collectors.toMap(FunctionSetDTO::getId, set -> set));
        return funcMap;
    }
}
