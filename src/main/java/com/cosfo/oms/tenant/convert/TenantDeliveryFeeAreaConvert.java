package com.cosfo.oms.tenant.convert;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.oms.common.utils.StringUtils;
import com.cosfo.oms.tenant.model.dto.DeliveryFeeAreaRuleDTO;
import com.cosfo.oms.tenant.model.dto.TenantDeliveryFeeAreaRuleDTO;
import com.cosfo.oms.tenant.model.po.TenantDeliveryFeeArea;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/15
 */
public class TenantDeliveryFeeAreaConvert {
    /**
     * 转化为TenantDeliveryFeeAreaRuleDTO
     *
     * @param tenantDeliveryFeeArea
     * @return
     */
    public static TenantDeliveryFeeAreaRuleDTO convertToDTO(TenantDeliveryFeeArea tenantDeliveryFeeArea){
        if (tenantDeliveryFeeArea == null) {
            return null;
        }

        TenantDeliveryFeeAreaRuleDTO tenantDeliveryFeeAreaRuleDTO = new TenantDeliveryFeeAreaRuleDTO();
        tenantDeliveryFeeAreaRuleDTO.setGroupId(tenantDeliveryFeeArea.getGroupId());
        tenantDeliveryFeeAreaRuleDTO.setProvince(tenantDeliveryFeeArea.getProvince());
        tenantDeliveryFeeAreaRuleDTO.setCity(tenantDeliveryFeeArea.getCity());
        tenantDeliveryFeeAreaRuleDTO.setArea(tenantDeliveryFeeArea.getArea());
        tenantDeliveryFeeAreaRuleDTO.setDefaultPrice(tenantDeliveryFeeArea.getDefaultPrice());
        if(!StringUtils.isEmpty(tenantDeliveryFeeArea.getRule())){
            List<DeliveryFeeAreaRuleDTO> deliveryFeeAreaRuleDTOS = JSONObject.parseArray(tenantDeliveryFeeArea.getRule(), DeliveryFeeAreaRuleDTO.class);
            tenantDeliveryFeeAreaRuleDTO.setDeliveryFeeAreaRuleDTOList(deliveryFeeAreaRuleDTOS);
        }

        return tenantDeliveryFeeAreaRuleDTO;
    }
}
