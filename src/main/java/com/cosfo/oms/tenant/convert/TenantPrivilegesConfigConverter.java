package com.cosfo.oms.tenant.convert;

import com.cosfo.oms.tenant.model.dto.TenantPrivilegesConfigDTO;
import com.cosfo.oms.tenant.model.po.TenantPrivilegesConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface TenantPrivilegesConfigConverter {

    TenantPrivilegesConfigConverter INSTANCE = Mappers.getMapper(TenantPrivilegesConfigConverter.class);

    @Mapping(target = "expireDate", source = "expirationTime")
    TenantPrivilegesConfigDTO toTenantPrivilegesConfigDTO(TenantPrivilegesConfig tenantPrivilegesConfig);


    @Mapping(target = "expirationTime", source = "expireDate")
    TenantPrivilegesConfig toTenantPrivilegesConfig(TenantPrivilegesConfigDTO tenantPrivilegesConfigDTO);
}
