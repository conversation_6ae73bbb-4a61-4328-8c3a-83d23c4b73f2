package com.cosfo.oms.tenant.convert;

import com.cosfo.oms.tenant.model.dto.CompanyAccountDTO;
import com.cosfo.oms.tenant.model.po.TenantCompanyAccount;
import com.cosfo.oms.tenant.model.vo.CompanyAccountVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface CompanyAccountMapper {

    CompanyAccountMapper INSTANCE = Mappers.getMapper(CompanyAccountMapper.class);

    CompanyAccountVO companyAccountToVO(TenantCompanyAccount companyAccount);

    TenantCompanyAccount companyAccountDtoToAccount(CompanyAccountDTO accountDTO);
}
