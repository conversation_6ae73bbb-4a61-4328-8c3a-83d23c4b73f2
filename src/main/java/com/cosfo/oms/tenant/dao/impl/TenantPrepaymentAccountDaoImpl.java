package com.cosfo.oms.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.oms.bill.model.dto.PrepaymentAccountQueryDTO;
import com.cosfo.oms.bill.model.vo.PrepaymentAmountVO;
import com.cosfo.oms.common.context.prepay.TenantPrepayPayableTargetEnum;
import com.cosfo.oms.tenant.dao.TenantPrepaymentAccountDao;
import com.cosfo.oms.tenant.mapper.TenantPrepaymentAccountMapper;
import com.cosfo.oms.tenant.model.po.TenantPrepaymentAccount;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 预付账户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Service
public class TenantPrepaymentAccountDaoImpl extends ServiceImpl<TenantPrepaymentAccountMapper, TenantPrepaymentAccount> implements TenantPrepaymentAccountDao {

    @Resource
    private TenantInfoService tenantInfoService;
    @Override
    public PrepaymentAmountVO queryTotalAmount(PrepaymentAccountQueryDTO queryDTO) {
        QueryWrapper<TenantPrepaymentAccount> accountQueryWrapper = new QueryWrapper<>();
        accountQueryWrapper.select("payable_target as payableTarget, sum(total_amount) as availableAmount");
        LambdaQueryWrapper<TenantPrepaymentAccount> accountLambdaQueryWrapper = accountQueryWrapper.lambda();
        accountLambdaQueryWrapper.eq(queryDTO.getPayee() != null, TenantPrepaymentAccount::getTenantId, queryDTO.getPayee());
        accountLambdaQueryWrapper.eq(queryDTO.getTenantId() != null, TenantPrepaymentAccount::getTenantId, queryDTO.getTenantId());
        if (!StringUtils.isEmpty(queryDTO.getShopName())) {
            List<Long> tenantIds = tenantInfoService.getTenantIdsByName(queryDTO.getShopName());
            if (CollectionUtils.isEmpty(tenantIds)) {
                return new PrepaymentAmountVO();
            }
            accountLambdaQueryWrapper.in(TenantPrepaymentAccount::getTenantId, tenantIds);
        }
        accountLambdaQueryWrapper.groupBy(TenantPrepaymentAccount::getPayableTarget);
        List<Map<String, Object>> result = listMaps(accountLambdaQueryWrapper);
        PrepaymentAmountVO amountVO = new PrepaymentAmountVO();
        if (CollectionUtils.isEmpty(result)) {
            return amountVO;
        }
        result.forEach(map -> {
            Integer payableTarget = (Integer) map.get("payableTarget");
            BigDecimal amount = (BigDecimal) map.getOrDefault("availableAmount", BigDecimal.ZERO);
            amountVO.setTotalAmount(amountVO.getTotalAmount().add(amount));
            TenantPrepayPayableTargetEnum targetEnum = TenantPrepayPayableTargetEnum.fromPayableTarget(payableTarget);
            switch (targetEnum) {
                case DIRECT_SUPPLY_AND_AGENT:
                    amountVO.setCommonAmount(amount);
                    break;
                case DIRECT_SUPPLY:
                    amountVO.setProductAmount(amount);
                    break;
                case AGENT_WAREHOUSE_EXPENSE:
                    amountVO.setAgentAmount(amount);
                    break;
                default:
            }
        });
        return amountVO;
    }

    @Override
    public List<TenantPrepaymentAccount> queryAccountList(PrepaymentAccountQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(queryDTO.getPayee() != null, TenantPrepaymentAccount::getTenantId, queryDTO.getPayee());
        accountLambdaQueryWrapper.eq(queryDTO.getTenantId() != null, TenantPrepaymentAccount::getTenantId, queryDTO.getTenantId());
        if (!StringUtils.isEmpty(queryDTO.getShopName())) {
            List<Long> tenantIds = tenantInfoService.getTenantIdsByName(queryDTO.getShopName());
            if (CollectionUtils.isEmpty(tenantIds)) {
                return Lists.newArrayList();
            }
            accountLambdaQueryWrapper.in(TenantPrepaymentAccount::getTenantId, tenantIds);
        }
        accountLambdaQueryWrapper.orderByDesc(Lists.newArrayList(TenantPrepaymentAccount::getLastChangeTime, TenantPrepaymentAccount::getId));
        return list(accountLambdaQueryWrapper);
    }

    @Override
    public Page<TenantPrepaymentAccount> queryAccountPage(PrepaymentAccountQueryDTO queryDTO) {
        LambdaQueryWrapper<TenantPrepaymentAccount> accountLambdaQueryWrapper = new LambdaQueryWrapper<>();
        accountLambdaQueryWrapper.eq(queryDTO.getPayee() != null, TenantPrepaymentAccount::getTenantId, queryDTO.getPayee());
        accountLambdaQueryWrapper.eq(queryDTO.getTenantId() != null, TenantPrepaymentAccount::getTenantId, queryDTO.getTenantId());
        if (!StringUtils.isEmpty(queryDTO.getShopName())) {
            List<Long> tenantIds = tenantInfoService.getTenantIdsByName(queryDTO.getShopName());
            if (CollectionUtils.isEmpty(tenantIds)) {
                return new Page<>();
            }
            accountLambdaQueryWrapper.in(TenantPrepaymentAccount::getTenantId, tenantIds);
        }
        accountLambdaQueryWrapper.orderByDesc(Lists.newArrayList(TenantPrepaymentAccount::getLastChangeTime, TenantPrepaymentAccount::getId));
        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), accountLambdaQueryWrapper);
    }


    @Override
    public TenantPrepaymentAccount getAccountByType(Long tenantId, Long supplierId, Integer type) {
        LambdaQueryWrapper<TenantPrepaymentAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrepaymentAccount::getTenantId, tenantId);
        queryWrapper.eq(TenantPrepaymentAccount::getSupplierTenantId, supplierId);
        queryWrapper.eq(TenantPrepaymentAccount::getPayableTarget, type);
        return getOne(queryWrapper);
    }

    @Override
    public Boolean updateAmount(Long accountId, BigDecimal amount) {
        LambdaUpdateWrapper<TenantPrepaymentAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TenantPrepaymentAccount::getId, accountId);
        updateWrapper.setSql("total_amount = total_amount  + " + amount + ", available_amount = available_amount + " + amount + ", last_change_time = now()");
        return update(updateWrapper);
    }

    @Override
    public Boolean lockAmount(Long accountId, BigDecimal amount) {
        return baseMapper.lockAmount(accountId, amount) == 1;
    }

    @Override
    public Boolean unlockAmount(Long accountId, BigDecimal amount) {
        return baseMapper.unlockAmount(accountId, amount) == 1;
    }

    @Override
    public Boolean successSub(Long accountId, BigDecimal amount) {
        return baseMapper.successSub(accountId, amount) == 1;
    }
}
