package com.cosfo.oms.tenant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.oms.tenant.model.po.TenantPrepaymentDailySnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 预付每日预付快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Mapper
public interface TenantPrepaymentDailySnapshotMapper extends BaseMapper<TenantPrepaymentDailySnapshot> {

    List<TenantPrepaymentDailySnapshot> selectDailySnapshot(@Param("startDate") LocalDate start, @Param("endDate") LocalDate end);

}
