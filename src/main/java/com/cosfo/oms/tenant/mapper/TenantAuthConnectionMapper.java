package com.cosfo.oms.tenant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.wechat.model.dto.TenantTemplateDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface TenantAuthConnectionMapper extends BaseMapper<TenantAuthConnection> {
    int deleteByPrimaryKey(Long id);

    int insertSelective(TenantAuthConnection record);

    TenantAuthConnection selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TenantAuthConnection record);

    int updateByPrimaryKey(TenantAuthConnection record);

    //根据appId
    TenantAuthConnection selectByAppId(String appId);

    //根据appIds查询所有租户id
    List<TenantAuthConnection> selectAllTenants(@Param("appIds") List<String> appIds);

    //选择待开发小程序用户列表
    List<TenantTemplateDto> selectTenantInfos(@Param("platformAppId") String platformAppId);

    /**
     * 根据租户id查询
     * @param tenantId
     * @return
     */
    TenantAuthConnection selectByTenantId(Long tenantId);
    /**
     * 根据租户idList查询
     * @param tenantIdList
     * @return
     */
    List<TenantAuthConnection> selectByTenantIdList(List<Long> tenantIdList);

    /**
     * 查询所有可用的小程序用户列表
     * @return
     */
    List<TenantAuthConnection> selectAllAuthConnection();

    List<Long> selectTenantInfosNeedInitTemplate(@Param("platformAppId") String platformAppId, @Param("createTemplateStatus") Integer status);

    /**
     * 批量更新模版状态
     * @param tenantIdList
     * @param status
     * @return
     */
    int batchUpdateCreateTemplateStatus(@Param("tenantIdList") List<Long> tenantIdList, @Param("createTemplateStatus") Integer status);
}