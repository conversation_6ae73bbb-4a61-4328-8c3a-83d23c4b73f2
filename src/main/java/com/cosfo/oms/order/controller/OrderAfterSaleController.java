package com.cosfo.oms.order.controller;

import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleAuditDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleBizDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleQueryDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleUnAuditModifyQuantityDTO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleVO;
import com.cosfo.oms.order.service.OrderAfterSaleDomainService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 18:05
 */
@RestController
@RequestMapping("/order/after/sale")
public class OrderAfterSaleController extends BaseController {


    @Resource
    private OrderAfterSaleDomainService orderAfterSaleService;

    /**
     * 订单售后列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public CommonResult getOrderListAfterSale(@RequestBody OrderAfterSaleQueryDTO afterSaleQueryDTO) {
        return orderAfterSaleService.getOrderListAfterSale(afterSaleQueryDTO, getMerchantInfoDTO());
    }


    /**
     * 售后订单列表导出订单
     */
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void exportOrdersAfterSale(@RequestBody OrderAfterSaleQueryDTO afterSaleQueryDto) {
        orderAfterSaleService.exportOrdersAfterSale(afterSaleQueryDto, getMerchantInfoDTO());
    }


    /**
     * 售后订单详情
     */
    @RequestMapping(value = "/details", method = RequestMethod.GET)
    public CommonResult getOrderDetails(@RequestParam("orderId") Long orderId) {
        return orderAfterSaleService.getOrderDetails(orderId);
    }

    /**
     * 售后订单详情售后商品列表
     */
    @RequestMapping(value = "/afterSaleCommodity", method = RequestMethod.GET)
    public CommonResult<List<OrderAfterSaleProductVO>> getOrderAfterSaleCommodity(@RequestParam("orderId") Long orderId) {
        return orderAfterSaleService.getOrderAfterSaleCommodity(orderId);
    }

    /**
     * 售后订单详情原订单列表
     */
    @RequestMapping(value = "/orderCommodity", method = RequestMethod.GET)
    public CommonResult getOrderCommodity(@RequestParam("orderId") Long orderId) {
        return orderAfterSaleService.getOrderCommodity(orderId);
    }

    /**
     * 售后订单审核提交
     *
     * @param orderAfterSaleAuditDTO
     */
    @RequestMapping(value = "/reviewSubmissions", method = RequestMethod.POST)
    public CommonResult ReviewSubmissions(@RequestBody OrderAfterSaleAuditDTO orderAfterSaleAuditDTO) {
        return orderAfterSaleService.reviewSubmissions(orderAfterSaleAuditDTO, getMerchantInfoDTO());
    }

    /**
     *  售后待审核订单修改数量
     * @param orderAfterSaleUnAuditModifyQuantityDTO
     * @return
     */
    @RequestMapping(value = "/upsert/modify-quantity", method = RequestMethod.POST)
    public CommonResult modifyQuantity(@RequestBody OrderAfterSaleUnAuditModifyQuantityDTO orderAfterSaleUnAuditModifyQuantityDTO) {
        return orderAfterSaleService.modifyQuantity(orderAfterSaleUnAuditModifyQuantityDTO, getMerchantInfoDTO());
    }

    /**
     * 取消售后单
     *
     * @param orderAfterSaleDTO
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public CommonResult cancel(@RequestBody OrderAfterSaleBizDTO orderAfterSaleDTO) {
        return orderAfterSaleService.cancel(orderAfterSaleDTO, getMerchantInfoDTO());
    }

    /**
     * 发起退款
     *
     * @param orderAfterSaleDTO
     */
    @RequestMapping(value = "/refund", method = RequestMethod.POST)
    public CommonResult refund(@RequestBody OrderAfterSaleBizDTO orderAfterSaleDTO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return orderAfterSaleService.refund(orderAfterSaleDTO, loginContextInfoDTO);
    }

    /**
     * 管理员发起售后
     *
     * @param orderAfterSaleDTO
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public CommonResult add(@RequestBody OrderAfterSaleBizDTO orderAfterSaleDTO) {
        LoginContextInfoDTO loginContextInfoDTO = getMerchantInfoDTO();
        return orderAfterSaleService.save(orderAfterSaleDTO, loginContextInfoDTO);
    }

    /**
     * 查询售后金额
     * @param orderAfterSaleVO
     * @return
     */
    @RequestMapping(value = "query/refund-price", method = RequestMethod.POST)
    public CommonResult<BigDecimal> queryRefundPrice(@RequestBody OrderAfterSaleVO orderAfterSaleVO) {
        BigDecimal refundPrice = orderAfterSaleService.calculateRefundPrice(orderAfterSaleVO.getOrderItemId(), orderAfterSaleVO.getAmount());
        return CommonResult.ok(refundPrice);
    }

    /**
     * 服务商确认
     * @param orderAfterSaleAuditDTO
     * @return
     */
    @RequestMapping(value = "/service-provider/review-submissions", method = RequestMethod.POST)
    public CommonResult<Boolean> serviceProviderReviewSubmissions(@RequestBody OrderAfterSaleAuditDTO orderAfterSaleAuditDTO) {
        return CommonResult.ok(orderAfterSaleService.serviceProviderReviewSubmissions(orderAfterSaleAuditDTO));
    }
}
