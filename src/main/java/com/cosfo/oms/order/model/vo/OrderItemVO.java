package com.cosfo.oms.order.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/17
 */
@Data
public class OrderItemVO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * sku编码
     */
    private Long itemId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 供应商商品skuId
     */
    private Long supplierSkuId;
    /**
     * 区域itemId
     */
    private Long areaItemId;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 供应商Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 可申请数量
     */
    private Integer enableApplyAmount;

    /**
     * 可售后数量
     */
    private Integer enableApplyQuantity;

    /**
     * 是否可申请售后
     */
    private Boolean enableApplyAfterSale;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 售后单位
     */
    private String afterSaleUnit;
    /**
     * afterSaleExpiryTime
     */
    private LocalDateTime afterSaleExpiryTime;

    /**
     * 配送方式 0 品牌方配送 1三方配送
     */
    private Integer deliveryType;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;
}
