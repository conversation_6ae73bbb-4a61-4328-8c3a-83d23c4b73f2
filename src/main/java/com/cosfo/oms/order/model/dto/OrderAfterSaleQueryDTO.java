package com.cosfo.oms.order.model.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleQueryDTO {

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;

    /**
     * 状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款 9 三方处理中 12 待确认
     */
    private Integer status;

    /**
     * 售后类型
     */
    private Integer afterSaleType;

    /**
     * 售后服务类型
     */
    private Integer serviceType;

    /**
     * 退款方式（取支付方式） 1、微信支付 2、账期 3、余额支付 4、支付宝支付 5、无需支付 6、线下支付 7、非现金支付 8、组合支付
     */
    private Integer payType;

    /**
     * 售后服务类型
     */
    private List<Integer> serviceTypes;

    /**
     * 下单日期
     */
    private LocalDateTime orderTime;

    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 店铺Id
     */
    private List<Long> storeIds;

    /**
     * 账户ID
     */
    private List<Long> accountIds;

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 供应商id
     */
    private Long supplierTenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 状态字符串
     */
    private List<Integer> statusList;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 标题
     */
    private String title;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品ids
     */
    private List<Long> itemIds;

    /**
     * 门店分组ids
     */
    private List<Long> merchantStoreGroupIds;

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 供应商ids
     */
    private List<Long> supplierIds;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 租户编号列表
     */
    private List<Long> tenantIds;

    /**
     * 最大主键ID(导出使用)
     */
    private Long maxPrimaryId;

}
