package com.cosfo.oms.order.model.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * order_item
 *
 * <AUTHOR>
@Data
public class OrderItem implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * sku编码
     */
    private Long itemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 可申请售后过期时间
     */
    private LocalDateTime afterSaleExpiryTime;

    private static final long serialVersionUID = 1L;
}
