package com.cosfo.oms.order.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * order
 *
 * <AUTHOR>
@Data
public class Order implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 店铺Id
     */
    private Long storeId;

    /**
     * 下单账号Id
     */
    private Long accountId;

    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 配送仓类型
     */
    private Byte warehouseType;

    /**
     * 应付价格
     */
    private BigDecimal payablePrice;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 支付方式 1，微信
     */
    private Integer payType;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 配送时间
     */
    private Date deliveryTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    private static final long serialVersionUID = 1L;
}
