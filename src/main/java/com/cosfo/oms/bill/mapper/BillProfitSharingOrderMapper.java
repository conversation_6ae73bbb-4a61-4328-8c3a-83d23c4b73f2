package com.cosfo.oms.bill.mapper;

import com.cosfo.oms.bill.model.po.BillProfitSharingOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BillProfitSharingOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BillProfitSharingOrder record);

    int insertSelective(BillProfitSharingOrder record);

    BillProfitSharingOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillProfitSharingOrder record);

    int updateByPrimaryKey(BillProfitSharingOrder record);


    /**
     * 根据订单Id查询
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    BillProfitSharingOrder queryByOrderIdAndTenantId(@Param("tenantId") Long tenantId,@Param("orderId") Long orderId);

    /**
     * 根据订单Id和供应商Id查询
     *
     * @param tenantId
     * @param orderId
     * @param supplierId
     * @return
     */
    BillProfitSharingOrder queryByTenantAndOrderAndSupplierId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId, @Param("supplierId") Long supplierId);
}