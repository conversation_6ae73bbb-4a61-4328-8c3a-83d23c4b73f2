package com.cosfo.oms.bill.model.dto;

import lombok.Data;
import lombok.ToString;
import net.xianmu.common.input.BasePageInput;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentRecordQueryDTO extends BasePageInput {

    /**
     * 预付时间范围开始
     */
    private LocalDateTime dateStart;

    /**
     * 预付时间范围结束
     */
    private LocalDateTime dateEnd;

    /**
     * 收款人
     */
    private Long supplierTenantId;

    /**
     * 提交人
     */
    private Long tenantId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * transaction_type 0、预付 1、退款
     */
    private Integer type;
}
