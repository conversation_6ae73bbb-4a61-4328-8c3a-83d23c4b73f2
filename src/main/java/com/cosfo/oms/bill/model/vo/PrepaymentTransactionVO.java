package com.cosfo.oms.bill.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentTransactionVO implements Serializable {

    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 商城名称
     */
    private String tenantName;

    /**
     * 供应商租户id
     */
    private Long supplierTenantId;

    private String supplierTenantName;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 收支类型 0、收入 1、支出
     */
    private Integer type;

    private String typeDesc;

    /**
     * 交易类型 0、预付 1、预付退款 2、直供货品消费 3、直供货品退款 4、运费 5、运费退款 6、代仓费用 7、代仓费用退款
     */
    private Integer transactionType;

    private String transactionTypeDesc;

    /**
     * 交易预付金额
     */
    private BigDecimal transactionAmount;

    /**
     * 关联订单单号/售后单号
     */
    private String associatedOrderNo;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
}
