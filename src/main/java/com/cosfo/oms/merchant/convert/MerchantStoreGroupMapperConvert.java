package com.cosfo.oms.merchant.convert;

import com.cosfo.oms.merchant.model.vo.MerchantStoreGroupVO;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupPageResultResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-06-01
 * @Description:
 */
@Mapper
public interface MerchantStoreGroupMapperConvert {

    MerchantStoreGroupMapperConvert INSTANCE = Mappers.getMapper(MerchantStoreGroupMapperConvert.class);


    List<MerchantStoreGroupVO> convertMerchantStoreGroup2VO(List<MerchantStoreGroupPageResultResp> merchantStoreGroupResultRespList);

    @Mapping(source = "merchantStoreGroupName", target = "name")
    MerchantStoreGroupVO convertMerchantStoreGroup2VO(MerchantStoreGroupPageResultResp merchantStoreGroupPageResultResp);


}
