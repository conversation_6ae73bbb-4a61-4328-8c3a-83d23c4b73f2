package com.cosfo.oms.merchant.convert;

import com.cosfo.oms.merchant.model.dto.MerchantDTO;
import com.cosfo.oms.merchant.model.po.Merchant;
import com.cosfo.oms.merchant.model.po.MerchantDeliveryFeeRule;
import com.cosfo.oms.merchant.model.po.MerchantDeliveryStepFee;
import com.cosfo.oms.tenant.model.dto.TenantAddMainInfoServiceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/3/28 16:31
 * @Description:
 */
@Mapper
public interface MerchantConvert {
    MerchantConvert INSTANCE = Mappers.getMapper(MerchantConvert.class);

    Merchant convert2Merchant(TenantAddMainInfoServiceDTO dto);

    MerchantDeliveryFeeRule copyMerchat(MerchantDeliveryFeeRule merchant);

    MerchantDeliveryStepFee copyStepFee(MerchantDeliveryStepFee stepFee);

    List<MerchantDTO> convert2Dtos(List<Merchant> merchants);

    MerchantDTO convert2Dto(Merchant merchant);

}
