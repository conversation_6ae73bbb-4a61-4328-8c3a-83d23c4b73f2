package com.cosfo.oms.merchant.mapper;

import com.cosfo.oms.merchant.model.po.StoreAddressAuditLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 门店地址申请变更记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Mapper
public interface StoreAddressAuditLogMapper extends BaseMapper<StoreAddressAuditLog> {

    /**
     * 根据时间返回查询出租户列表
     *
     * @param startOfDay
     * @param endOfDay
     * @return
     */
    List<Long> queryTenantIdsByTime(@Param("startOfDay") LocalDateTime startOfDay, @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 根据tms单号查询结果
     *
     * @param auditNo
     * @return
     */
    StoreAddressAuditLog queryByAuditNo(@Param("auditNo") String auditNo);

    /**
     * 查询待审核的门店审批记录
     *
     * @param tenantId
     * @param storeId
     * @param auditStatus
     * @return
     */
    StoreAddressAuditLog queryWaitStatusLogByStoreId(@Param("tenantId") Long tenantId, @Param("storeId") Long storeId,
                                                     @Param("auditStatus") Integer auditStatus);
}
