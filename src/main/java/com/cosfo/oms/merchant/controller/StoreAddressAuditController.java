package com.cosfo.oms.merchant.controller;

import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.merchant.model.dto.StoreAddressAuditDTO;
import com.cosfo.oms.merchant.model.dto.StoreAddressQueryDTO;
import com.cosfo.oms.merchant.model.vo.StoreAddressAuditDetailVO;
import com.cosfo.oms.merchant.model.vo.StoreAddressAuditVO;
import com.cosfo.oms.merchant.service.StoreAddressAuditService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 地址变更功能
 *
 * <AUTHOR>
 * @description
 * @date 2024/1/25 11:34
 */
@RestController
@RequestMapping("/store/address")
public class StoreAddressAuditController extends BaseController {

    @Resource
    private StoreAddressAuditService storeAddressAuditService;

    /**
     * 审核列表
     *
     * @param storeAddressQueryDTO
     * @return
     */
    @RequestMapping(value = "/audit/list", method = RequestMethod.POST)
    public CommonResult<PageInfo<StoreAddressAuditVO>> auditList(@Valid @RequestBody StoreAddressQueryDTO storeAddressQueryDTO) {
        return CommonResult.ok(storeAddressAuditService.auditList(storeAddressQueryDTO));
    }

    /**
     * 审核详情
     *
     * @param auditId
     * @return
     */
    @RequestMapping(value = "/audit/detail", method = RequestMethod.GET)
    public CommonResult<StoreAddressAuditDetailVO> auditDetail(Long auditId) {
        return CommonResult.ok(storeAddressAuditService.auditDetail(auditId));
    }

    /**
     * 审核地址变更
     *
     * @param storeAddressAuditDTO
     * @return
     */
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    public CommonResult audit(@Valid @RequestBody StoreAddressAuditDTO storeAddressAuditDTO) {
        storeAddressAuditService.audit(storeAddressAuditDTO);
        return CommonResult.ok();
    }


    /**
     * 审核列表导出
     *
     * @return
     */
    @PostMapping(value = "/audit/export-async")
    public CommonResult<Long> auditExport(@Valid @RequestBody StoreAddressQueryDTO storeAddressQueryDTO) {
        Long resId = storeAddressAuditService.auditExport(storeAddressQueryDTO);
        return CommonResult.ok(resId);
    }
}
