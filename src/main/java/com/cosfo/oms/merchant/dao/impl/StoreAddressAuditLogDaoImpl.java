package com.cosfo.oms.merchant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.common.util.DateUtil;
import com.cosfo.oms.common.context.StoreAddressAuditStatusEnum;
import com.cosfo.oms.merchant.dao.StoreAddressAuditLogDao;
import com.cosfo.oms.merchant.mapper.StoreAddressAuditLogMapper;
import com.cosfo.oms.merchant.model.dto.StoreAddressAuditDTO;
import com.cosfo.oms.merchant.model.dto.StoreAddressCheckQueryDTO;
import com.cosfo.oms.merchant.model.po.StoreAddressAuditLog;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 门店地址申请变更记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-25
 */
@Service
public class StoreAddressAuditLogDaoImpl extends ServiceImpl<StoreAddressAuditLogMapper, StoreAddressAuditLog> implements StoreAddressAuditLogDao {

    @Override
    public List<Long> queryTenantIdsByTime(LocalDate startTime, LocalDate endTime) {
        return baseMapper.queryTenantIdsByTime(DateUtil.startOfDay(startTime), DateUtil.endOfDay(endTime));
    }

    @Override
    public Page<StoreAddressAuditLog> getStoreAddressAuditLogPage(StoreAddressCheckQueryDTO queryDTO) {
        LambdaQueryWrapper<StoreAddressAuditLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(StoreAddressAuditLog::getCreateTime, DateUtil.startOfDay(queryDTO.getStartTime()), DateUtil.endOfDay(queryDTO.getEndTime()));
        queryWrapper.in(StoreAddressAuditLog::getTenantId, queryDTO.getTenantIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryDTO.getStoreIds()), StoreAddressAuditLog::getStoreId, queryDTO.getStoreIds());
        queryWrapper.eq(Objects.nonNull(queryDTO.getAuditStatus()), StoreAddressAuditLog::getAuditStatus, queryDTO.getAuditStatus());
        queryWrapper.eq(Objects.nonNull(queryDTO.getTenantType()), StoreAddressAuditLog::getTenantType, queryDTO.getTenantType());
        queryWrapper.eq(StringUtils.isNotEmpty(queryDTO.getPhone()), StoreAddressAuditLog::getContactPhone, queryDTO.getPhone());
        queryWrapper.orderByDesc(StoreAddressAuditLog::getCreateTime);

        return page(new Page<>(queryDTO.getPageIndex(), queryDTO.getPageSize()), queryWrapper);
    }

    @Override
    public Boolean updateAuditResult(StoreAddressAuditDTO storeAddressAuditDTO, MerchantAddressResultResp resp) {
        LambdaUpdateWrapper<StoreAddressAuditLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StoreAddressAuditLog::getId, storeAddressAuditDTO.getAuditId());
        updateWrapper.set(StoreAddressAuditLog::getAuditRemark, storeAddressAuditDTO.getAuditRemark());
        updateWrapper.set(StoreAddressAuditLog::getAuditTime, LocalDateTime.now());
        // 审核时原地址快照记录
        updateWrapper.set(StoreAddressAuditLog::getProvince, resp.getProvince());
        updateWrapper.set(StoreAddressAuditLog::getCity, resp.getCity());
        updateWrapper.set(StoreAddressAuditLog::getArea, resp.getArea());
        updateWrapper.set(StoreAddressAuditLog::getAddress, resp.getAddress());
        updateWrapper.set(StoreAddressAuditLog::getHouseNumber, resp.getHouseNumber());
        updateWrapper.set(StoreAddressAuditLog::getPoiNote, resp.getPoiNote());

        // 审核时修改地址信息
        if (storeAddressAuditDTO.getAuditSuccess()) {
            updateWrapper.set(StoreAddressAuditLog::getModifyProvince, storeAddressAuditDTO.getProvince());
            updateWrapper.set(StoreAddressAuditLog::getModifyCity, storeAddressAuditDTO.getCity());
            updateWrapper.set(StoreAddressAuditLog::getModifyArea, storeAddressAuditDTO.getArea());
            updateWrapper.set(StoreAddressAuditLog::getModifyAddress, storeAddressAuditDTO.getAddress());
            updateWrapper.set(StoreAddressAuditLog::getModifyHouseNumber, storeAddressAuditDTO.getHouseNumber());
            updateWrapper.set(StoreAddressAuditLog::getModifyPoiNote, storeAddressAuditDTO.getPoiNote());
            updateWrapper.set(StoreAddressAuditLog::getDistance, storeAddressAuditDTO.getDistance());
        }

        Integer auditStatus = storeAddressAuditDTO.getAuditSuccess() ? StoreAddressAuditStatusEnum.AUDIT_SUCCESS.getStatus() : StoreAddressAuditStatusEnum.AUDIT_FAIL.getStatus();
        updateWrapper.set(StoreAddressAuditLog::getAuditStatus, auditStatus);
        return update(updateWrapper);
    }

    @Override
    public StoreAddressAuditLog queryByAuditNo(String auditNo) {
        return baseMapper.queryByAuditNo(auditNo);
    }

    @Override
    public StoreAddressAuditLog queryWaitStatusLogByStoreId(Long tenantId, Long storeId) {
        return baseMapper.queryWaitStatusLogByStoreId(tenantId, storeId, StoreAddressAuditStatusEnum.IN_AUDIT.getStatus());
    }
}
