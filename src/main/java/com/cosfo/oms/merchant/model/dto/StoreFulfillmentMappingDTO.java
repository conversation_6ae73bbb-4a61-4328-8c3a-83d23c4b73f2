package com.cosfo.oms.merchant.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.schedulerx.shade.org.apache.commons.lang.math.NumberUtils;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class StoreFulfillmentMappingDTO implements Serializable {

    /**
     * 商城名称
     */
    @ExcelProperty("商城名称")
    private String tenantName;

    /**
     * 门店ID
     */
    @ExcelProperty("门店ID")
    private String storeIdStr;

    /**
     * 门店名称
     */
    @ExcelProperty("门店名称")
    private String storeName;

    /**
     * 鲜沐履约方式
     */
    @ExcelProperty("鲜沐履约方式")
    private String fulfillmentType;

    /**
     * 城配仓（仅快递履约）
     */
    @ExcelProperty("城配仓（仅快递履约）")
    private String warehouseName;

    @ExcelIgnore
    private String errorMsg;

    @ExcelIgnore
    private Long storeId;

    @ExcelIgnore
    private Long tenantId;

    @ExcelIgnore
    private Integer warehouseNo;

    public boolean validateFields() {
        if (StringUtils.isEmpty(tenantName)) {
            this.errorMsg = "商城名称不能为空";
            return false;
        }
        if (StringUtils.isEmpty(storeIdStr)) {
            this.errorMsg = "门店ID不能为空";
            return false;
        } else if (!NumberUtils.isNumber(storeIdStr)) {
            this.errorMsg = "门店ID必须是数值类型";
            return false;
        }

        if (StringUtils.isEmpty(storeName)) {
            this.errorMsg = "门店名称不能为空";
            return false;
        }

        if (StringUtils.isEmpty(fulfillmentType)) {
            this.errorMsg = "鲜沐履约方式不能为空";
            return false;
        }

        this.errorMsg = null;
        return true;
    }

    public void transferField() {
        if (!StringUtils.isEmpty(storeIdStr) && NumberUtils.isNumber(storeIdStr)) {
            storeId = Long.valueOf(storeIdStr);
        }
    }
}
