package com.cosfo.oms.merchant.model.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-28
 * @Description:
 */
@Data
public class MerchantStoreUpdateDTO implements Serializable {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 注册时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系人
     */
    private List<MerchantContactDTO> contactList;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 审核标识 0、不通过 1、通过
     */
    private Integer auditFlag;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 账期权限 0开启1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付
     */
    private Integer onlinePayment;

    /**
     * 1 开店 2 关店
     */
    private Integer operateStatus;

    /**
     * 手机号
     */
    private String accountPhone;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 分组Id
     */
    private Long groupId;

    /**
     * 分组Id
     */
    private List<Long> groupIds;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 是否为分组 -1
     */
    private Integer havingGroup;

    /**
     * 是否供应
     */
    private Integer supplyStatus;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 账号list
     */
    private List<MerchantStoreAccountDTO> accountList;

    /**
     * 分组ids
     */
    private List<Long> merchantStoreGroupIds;

    /**
     * 履约方式 1:城配履约、2:快递履约
     */
    private Integer fulfillmentType;

    /**
     * 城配仓编码
     */
    private Integer warehouseNo;

    /**
     * 配送周期
     */
    private DeliveryRuleDTO deliveryRuleDTO;

    /**
     * 地址备注
     */
    private AddressRemarkDTO addressRemarkDTO;

    /**
     * 指定城配仓标识 true 指定城配仓 false 非指定城配仓
     */
    private Boolean isStoreNoAppointment;

    private static final long serialVersionUID = 1L;
}
