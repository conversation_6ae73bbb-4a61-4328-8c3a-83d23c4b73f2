package com.cosfo.oms.merchant.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Author: fansongsong
 * @Date: 2024-01-25
 * @Description:
 */
@Data
public class StoreAddressAuditDTO {

    /**
     * 审批单ID
     */
    @NotNull(message = "auditId不能为空")
    private Long auditId;

    /**
     * 审核结果：true:通过;false:不通过
     */
    @NotNull(message = "auditSuccess不能为空")
    private Boolean auditSuccess;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 商家腾讯地图坐标
     */
    private String poiNote;

    /**
     * 审核备注
     */
    private String auditRemark;


    /**
     * 距离(例:100m)
     */
    @Size(
            max = 50,
            message = "distance在50个字符内"
    )
    private String distance;
}
