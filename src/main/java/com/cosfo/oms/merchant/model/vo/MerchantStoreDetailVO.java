package com.cosfo.oms.merchant.model.vo;

import com.cosfo.oms.merchant.model.dto.AddressRemarkDTO;
import com.cosfo.oms.merchant.model.dto.DeliveryRuleDTO;
import com.cosfo.oms.merchant.model.dto.MerchantContactDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreAccountDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-28
 * @Description:
 */
@Data
public class MerchantStoreDetailVO {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户类型：0-品牌方,1-鲜沐,2-帆台,3-外单
     */
    private Integer tenantType;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店类型：0、直营店 1、加盟店 2、托管店
     */
    private Integer type;


    /**
     * 门店状态：0、审核中 1、审核通过 2、审核拒绝 3、关店
     */
    private Integer status;

    /**
     * 门店状态描述
     */
    private String statusDesc;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 注册手机号
     */
    private String phone;

    /**
     * 联系人Id
     */
    private Long contactId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 收货地址
     */
    private String deliveryAddress;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 门牌号
     */
    private String houseNumber;

    /**
     * 联系人
     */
    private List<MerchantContactDTO> contactList;

    /**
     * 账户名
     */
    private String accountName;

    /**
     * 0 店长 1 店员
     */
    private Integer accountType;

    /**
     * 账户注册时间
     */
    private LocalDateTime accountRegisterTime;

    /**
     * 账户审核时间
     */
    private LocalDateTime accountAuditTime;

    /**
     * 账户手机号
     */
    private String accountPhone;

    /**
     * 账户状态 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer accountStatus;

    /**
     * 0、否 1、是
     */
    private Integer defaultFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核标识 0、不通过 1、通过
     */
    private Integer auditFlag;

    /**
     * poi地址
     */
    private String poiNote;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 账期权限 0开启1关闭
     */
    private Integer billSwitch;

    /**
     * 在线支付 0开启1关闭
     */
    private Integer onlinePayment;

    /**
     * 余额权限 0、关闭 1、开启
     */
    private Integer balanceAuthority;

    /**
     * 余额
     */
    private BigDecimal balance;

    private String billPermissions;

    /**
     * 1 开店 2 关店
     */
    private Integer operateStatus;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 是否供应当前城市 0是1否
     */
    private Integer supplyStatus;

    /**
     * 门店分组Id
     */
    private Long groupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 价格策略映射Id
     */
    private Long areaItemMappingId;

    /**
     * 账号集合
     */
    private List<MerchantStoreAccountDTO> accountList;

    /**
     * 门店分组名称
     */
    private String merchantStoreGroupName;

    /**
     * 城配仓编号
     */
    private Integer warehouseNo;

    /**
     * 城配仓名称
     */
    private String warehouseName;

    /**
     * 履约方式 1:城配履约、2:快递履约
     */
    private Integer fulfillmentType;

    /**
     * 配送周期
     */
    private DeliveryRuleDTO deliveryRuleDTO;

    /**
     * 是否允许编辑：
     * true-允许编辑：如果是人为指定城配仓的时候就可以编辑
     * false：不可以编辑：通过地址自己带出城配仓的时候
     */
    private Boolean isStoreNoAppointment;

    /**
     * 地址备注
     */
    private AddressRemarkDTO addressRemarkDTO;
}
