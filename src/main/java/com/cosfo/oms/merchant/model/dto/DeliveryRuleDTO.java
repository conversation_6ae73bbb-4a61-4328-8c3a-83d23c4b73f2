package com.cosfo.oms.merchant.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/4/18 16:44
 */
@Data
public class DeliveryRuleDTO implements Serializable {

    private static final long serialVersionUID = 58544380166400142L;

    /**
     * 周期方案 1周计算 2间隔计算
     */
    private Integer frequentMethod;

    /**
     * 周期方案为1周计算 不能为空
     * 周的配送周期 0每天 1周一 依次,多个逗号分隔
     */
    private String weekDeliveryFrequent;

    /**
     * 周期方案为2间隔计算 不能为空
     * 配送间隔周期
     */
    private Integer deliveryFrequentInterval;

    /**
     * 周期方案为2间隔计算 不能为空
     * 开始计算日期
     */
    private LocalDate beginCalculateDate;
}
