package com.cosfo.oms.merchant.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门店账号 dto
 * <AUTHOR>
 * @date 2022/12/2 16:11
 */
@Data
public class MerchantStoreAccountDTO {

    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号类型：0、店长 1、店员
     */
    private Integer type;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * open id
     */
    private String openId;

    /**
     * union id
     */
    private String unionId;

    /**
     * 0、审核中 1、审核通过 2、审核拒绝
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * @see com.cosfo.manage.common.context.MerchantStoreAccountDeleteFlagEnum
     */
    private Integer deleteFlag;

}
