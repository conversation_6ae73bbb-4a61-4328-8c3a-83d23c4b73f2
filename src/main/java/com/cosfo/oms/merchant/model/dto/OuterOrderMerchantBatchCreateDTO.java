package com.cosfo.oms.merchant.model.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cosfo.oms.common.context.usercenter.MerchantStoreEnum;
import com.cosfo.oms.merchant.convert.MerchantStoreMapperConvert;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.download.support.dto.ImportExcelBaseDTO;
import net.xianmu.usercenter.client.merchant.enums.MerchantAddressEnums;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-12-28
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OuterOrderMerchantBatchCreateDTO extends ImportExcelBaseDTO {

    /**
     * 序号
     */
    @ExcelIgnore
    private int num;

    /**
     * 租户id
     */
    @ExcelIgnore
    private Long tenantId;

    /**
     * 区代id
     */
    @ExcelIgnore
    private Long regionalId;

    /**
     * 公司简称
     */
    @ExcelProperty("公司简称")
    @NotEmpty(message = "公司简称不能为空")
    private String tenantName;

    /**
     * 门店名称
     */
    @ExcelProperty("门店名称")
    @NotEmpty(message = "门店名称不能为空")
    private String storeName;

    /**
     * 联系电话
     */
    @ExcelProperty("联系电话")
    @NotEmpty(message = "联系电话不能为空")
    private String phone;

    /**
     * 联系人
     */
    @ExcelProperty("联系人")
    @NotEmpty(message = "联系人不能为空")
    private String contactName;


    /**
     * 省份
     */
    @ExcelProperty("省份")
    @NotEmpty(message = "省份不能为空")
    private String province;

    /**
     * 城市
     */
    @ExcelProperty("城市")
    @NotEmpty(message = "城市不能为空")
    private String city;

    /**
     * 区域
     */
    @ExcelProperty("区域")
    @NotEmpty(message = "区域不能为空")
    private String area;

    /**
     * 	详细地址
     */
    @ExcelProperty("详细地址")
    @NotEmpty(message = "详细地址不能为空")
    private String address;


}
