package com.cosfo.oms.merchant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.oms.common.context.BalanceAuthorityTypeEnum;
import com.cosfo.oms.common.context.BillSwitchEnum;
import com.cosfo.oms.common.context.FileDownloadTypeEnum;
import com.cosfo.oms.common.context.TenantTypeEnum;
import com.cosfo.oms.common.context.usercenter.DefaultFlagEnum;
import com.cosfo.oms.common.context.usercenter.MerchantAccountTypeEnum;
import com.cosfo.oms.common.context.usercenter.MerchantContactEnum;
import com.cosfo.oms.common.context.usercenter.MerchantStoreEnum;
import com.cosfo.oms.facade.UserCenterMerchantStoreFacade;
import com.cosfo.oms.facade.UserCenterTenantFacade;
import com.cosfo.oms.facade.dto.tenant.TenantInputQueryDTO;
import com.cosfo.oms.mapper.CommonLocationCityMapper;
import com.cosfo.oms.merchant.model.dto.MerchantContactDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreAccountDTO;
import com.cosfo.oms.merchant.model.dto.MerchantStoreDTO;
import com.cosfo.oms.merchant.model.dto.OuterOrderMerchantBatchCreateDTO;
import com.cosfo.oms.model.dto.LocationCityDTO;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.download.support.dto.DownloadCenterDataMsg;
import net.xianmu.download.support.handler.DownloadCenterImportDefaultHandler;
import net.xianmu.usercenter.client.merchant.enums.MerchantAddressEnums;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantContactCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreAccountCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreCommandReq;
import net.xianmu.usercenter.client.merchant.req.MerchantStoreDomainCommandReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreBatchImportResp;
import net.xianmu.usercenter.client.regional.resp.RegionalOrganizationResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/4/24 15:57
 */
@Component
@Slf4j
public class OuterOrderMerchantStoreHandler extends DownloadCenterImportDefaultHandler<OuterOrderMerchantBatchCreateDTO> {

    @Resource
    private CommonLocationCityMapper commonLocationCityMapper;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private UserCenterMerchantStoreFacade userCenterMerchantStoreFacade;

    @Override
    protected void dealExcelData(List<OuterOrderMerchantBatchCreateDTO> list, DownloadCenterDataMsg downloadCenterDataMsg) {
        log.info("开始批量导入门店数据, data:{}", JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.warn("待处理列表为空!");
            return;
        }
        this.initNum(list);

        // 1.先进行列表内数据检验、剔除掉脏数据
        this.innerValidate(list);

        // 2.通过名称+类型 获取租户
        this.validateTenant(list);

        // 3.根据租户列表获取区域组织
        this.validateRegionalOrganization(list);

        // 4.组装数据、批量调用
        this.innerBatchCreate(list);

    }


    private void innerBatchCreate(List<OuterOrderMerchantBatchCreateDTO> list) {
        log.info("【批量导入】开始组装客户信息");
        List<MerchantStoreDomainCommandReq> reqs = new ArrayList<>();
        List<OuterOrderMerchantBatchCreateDTO> merchantBatchCreateDTOList = list.stream().filter(dto -> dto.getErrorMsg() == null).collect(Collectors.toList());
        for (OuterOrderMerchantBatchCreateDTO dto : merchantBatchCreateDTOList) {
            MerchantStoreDomainCommandReq domainCommandReq = transferToStoreDTO(dto);
            reqs.add(domainCommandReq);
        }

        if (CollUtil.isNotEmpty(reqs)) {
            List<MerchantStoreBatchImportResp> merchantStoreInfoBatch = userCenterMerchantStoreFacade.createMerchantStoreInfoBatch(reqs);
            if (CollUtil.isNotEmpty(merchantStoreInfoBatch)) {
                Map<String, String> errorMap = merchantStoreInfoBatch.stream().filter(e -> Objects.isNull(e.getStoreId())).collect(Collectors.toList()).stream().collect(Collectors.toMap(dto -> dto.getTenantId() + dto.getStoreName(), MerchantStoreBatchImportResp::getErrorDesc));
                list.forEach(dto -> {
                    String reason = errorMap.get(dto.getTenantId() + dto.getStoreName());
                    if (Strings.isNotBlank(reason)) {
                        dto.setErrorMsg(reason);
                    }
                });
            }
        }
    }


    private void initNum(List<OuterOrderMerchantBatchCreateDTO> list) {
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setNum(i);
        }
    }

    private void validateRegionalOrganization(List<OuterOrderMerchantBatchCreateDTO> list){
        log.info("【批量导入】开始验证租户下区代信息");
        List<Long> tenantIds = list.stream().map(OuterOrderMerchantBatchCreateDTO::getTenantId).collect(Collectors.toList());
        List<RegionalOrganizationResultResp> regionalOrganizationResultResps = userCenterMerchantStoreFacade.saasGetRegionalByTenantIds(tenantIds);
        Map<Long, Long> map = regionalOrganizationResultResps.stream().collect(Collectors.toMap(RegionalOrganizationResultResp::getTenantId, RegionalOrganizationResultResp::getId));
        list.stream().filter(dto -> dto.getTenantId() != null).forEach(dto -> {
            Long regionalId = map.get(dto.getTenantId());
            if(regionalId == null) {
                dto.setErrorMsg("客户信息存在异常");
            } else {
                dto.setRegionalId(regionalId);
            }
        });

    }



    private void innerValidate(List<OuterOrderMerchantBatchCreateDTO> list) {
        log.info("【批量导入】开始内部校验");
        List<LocationCityDTO> locationCityDTOS = commonLocationCityMapper.queryAllCommonLocationCity();
        List<String> allProvinces = commonLocationCityMapper.queryAllCommonLocationProvince();
        List<String> allCity = locationCityDTOS.stream().map(LocationCityDTO::getName).collect(Collectors.toList());
        Set<String> repeatNames = list.stream().collect(Collectors.groupingBy(dto -> dto.getTenantName() + dto.getStoreName()))
                .values()
                .stream().filter(v -> v.size() > 1).reduce(new ArrayList<>(), (a, b) -> {
                    a.addAll(b);
                    return a;
                }).stream().map(dto -> dto.getTenantName() + dto.getStoreName()).collect(Collectors.toSet());

        for (OuterOrderMerchantBatchCreateDTO dto : list) {
            // 省的名称是否正确
            if (!allProvinces.contains(dto.getProvince())) {
                dto.setErrorMsg("省份不存在");
                continue;
            }
            // 增加城市名称是否正确
            if (!allCity.contains(dto.getCity())) {
                dto.setErrorMsg("城市不存在");
                continue;
            }
            if (repeatNames.contains(dto.getTenantName() + dto.getStoreName())) {
                dto.setErrorMsg("excel内店铺名称重复");
                continue;
            }
        }
    }


    private void validateTenant(List<OuterOrderMerchantBatchCreateDTO> list) {
        log.info("【批量导入】开始验证租户信息");
        List<String> collect = list.stream().filter(dto -> dto.getErrorMsg() == null).map(OuterOrderMerchantBatchCreateDTO::getTenantName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            list.stream().filter(dto -> dto.getErrorMsg() == null).forEach(dto -> dto.setErrorMsg("公司不存在"));
            return;
        }
        List<TenantResultResp> tenantInfos = tenantInfoService.getTenantInfosByQuery(TenantInputQueryDTO.builder().exactTenantNameList(collect).type(TenantTypeEnum.OUTER_ORDER.getCode()).build());
        Map<String, TenantResultResp> tenantResultRespMap = tenantInfos.stream().collect(Collectors.toMap(TenantResultResp::getTenantName, Function.identity()));

        // 初始化租户id
        list.stream().filter(dto -> dto.getErrorMsg() == null).forEach(dto -> {
            TenantResultResp tenantResultResp = tenantResultRespMap.get(dto.getTenantName());
            if (tenantResultResp != null) {
                dto.setTenantId(tenantResultResp.getId());
            } else {
                dto.setErrorMsg("公司不存在");
            }
        });
    }


    private MerchantStoreDomainCommandReq transferToStoreDTO(OuterOrderMerchantBatchCreateDTO registerInfoDTO) {
        Long tenantId = registerInfoDTO.getTenantId();
        MerchantStoreDomainCommandReq domainCommandReq = new MerchantStoreDomainCommandReq();
        MerchantStoreCommandReq storeCommandReq = new MerchantStoreCommandReq();
        storeCommandReq.setTenantId(tenantId);
        storeCommandReq.setRegionalId(registerInfoDTO.getRegionalId());
        storeCommandReq.setStatus(MerchantStoreEnum.Status.IN_AUDIT.getCode());
        storeCommandReq.setStoreName(registerInfoDTO.getStoreName());
        storeCommandReq.setBillSwitch(BillSwitchEnum.SHUTDOWN.getCode());
        storeCommandReq.setBalanceAuthority(BalanceAuthorityTypeEnum.CLOSE_BALANCE_AUTH.getType());
        // 默认都保留在线支付能力
        storeCommandReq.setOnlinePayment(1);
        storeCommandReq.setType(MerchantStoreEnum.Type.UN_KNOW.getCode());
        domainCommandReq.setMerchantStore(storeCommandReq);

        // 账户
        List<MerchantStoreAccountCommandReq> merchantStoreAccountList = Lists.newArrayList();
        MerchantStoreAccountCommandReq merchantStoreAccount = new MerchantStoreAccountCommandReq();
        merchantStoreAccount.setTenantId(registerInfoDTO.getTenantId());
        merchantStoreAccount.setAccountName(registerInfoDTO.getContactName());
        merchantStoreAccount.setPhone(registerInfoDTO.getPhone());
        merchantStoreAccount.setType(MerchantAccountTypeEnum.MANAGER.getType());
        merchantStoreAccount.setRegisterTime(LocalDateTime.now());
        merchantStoreAccount.setAuditTime(LocalDateTime.now());
        merchantStoreAccount.setStatus(MerchantStoreEnum.Status.AUDIT_SUCCESS.getCode());
        merchantStoreAccountList.add(merchantStoreAccount);
        domainCommandReq.setMerchantStoreAccountList(merchantStoreAccountList);


        // 注册地址信息
        List<MerchantAddressCommandReq> merchantAddressCommandReqList = Lists.newArrayList();
        MerchantAddressCommandReq merchantAddress = new MerchantAddressCommandReq();
        merchantAddress.setTenantId(registerInfoDTO.getTenantId());
        merchantAddress.setProvince(registerInfoDTO.getProvince());
        merchantAddress.setCity(registerInfoDTO.getCity());
        merchantAddress.setArea(registerInfoDTO.getArea());
        merchantAddress.setAddress(registerInfoDTO.getAddress());
        merchantAddress.setDefaultFlag(MerchantAddressEnums.DefaultFlag.DEFAULT.getCode());
        merchantAddress.setStatus(MerchantAddressEnums.status.NORMAL.getCode());
        merchantAddressCommandReqList.add(merchantAddress);

        List<MerchantContactCommandReq> contactList = new ArrayList<>();
        MerchantContactCommandReq merchantContact = new MerchantContactCommandReq();
        merchantContact.setTenantId(registerInfoDTO.getTenantId());
        merchantContact.setAddressId(merchantAddress.getId());
        merchantContact.setName(registerInfoDTO.getContactName());
        merchantContact.setPhone(registerInfoDTO.getPhone());
        merchantContact.setDefaultFlag(MerchantContactEnum.DEFAULT.getType());
        contactList.add(merchantContact);
        merchantAddress.setMerchantContactList(contactList);
        domainCommandReq.setMerchantAddressList(Collections.singletonList(merchantAddress));

        return domainCommandReq;
    }


    @Override
    public DownloadCenterEnum.RequestSource getSource() {
        return DownloadCenterEnum.RequestSource.SAAS_BOSS;
    }

    @Override
    public Integer getBizType() {
        return FileDownloadTypeEnum.OUTER_ORDER_MERCHANT_IMPORT.getType();
    }
}
