package com.cosfo.oms.model.po;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * user
 *
 * <AUTHOR>
@Data
public class User implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 登录手机号
     */
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 状态 0，待审核 1，审核通过 2，审核失败
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}