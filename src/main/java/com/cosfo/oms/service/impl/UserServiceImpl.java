package com.cosfo.oms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.oms.common.context.UserStatusEnum;
import com.cosfo.oms.common.exception.DefaultServiceException;
import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.AssertParam;
import com.cosfo.oms.common.utils.MD5Util;
import com.cosfo.oms.common.utils.RedisUtils;
import com.cosfo.oms.mapper.UserMapper;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.model.dto.UserDTO;
import com.cosfo.oms.model.po.User;
import com.cosfo.oms.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/16
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    @Resource
    private UserMapper userMapper;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public ResultDTO login(UserDTO userDTO) {
//        AssertParam.notNull(userDTO.getPhone(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "手机号不能为空");
//        AssertParam.notNull(userDTO.getPassword(), ResultDTOEnum.PARAMETER_MISSING.getCode(), "密码不能为空");
//
//        // 查询用户信息
//        User user = userMapper.selectByPhone(userDTO.getPhone());
//        if (user == null || !user.getPassword().equals(MD5Util.string2MD5(userDTO.getPassword()))) {
//            throw new DefaultServiceException(ResultDTOEnum.USER_OR_PASSWORD_WRONG.getCode(), ResultDTOEnum.USER_OR_PASSWORD_WRONG.getMessage());
//        }
//
//        if (user.getStatus().equals(UserStatusEnum.AUDIT_SUCCESS.getCode())) {
//            throw new DefaultServiceException(ResultDTOEnum.ACCOUNT_FAILURE.getCode(), ResultDTOEnum.ACCOUNT_FAILURE.getMessage());
//        }
//
//        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
//        loginContextInfoDTO.setUserName(user.getUserName());
//        loginContextInfoDTO.setPhone(user.getPhone());
//        loginContextInfoDTO.setId(user.getId());
//        // 生成随机数
//        String randomNum = MD5Util.createRandomNum();
//
//        String jwt = JwtUtils.generateToken(user.getId(), user.getPhone(), randomNum);
//        loginContextInfoDTO.setJwtToken(jwt);
//        // 简化token长度
//        String accessToken = JwtUtils.TOKEN_PREFIX + MD5Util.string2MD5(jwt);
//
//        // 数据缓存
//        redisUtils.set(accessToken, JSONObject.toJSONString(loginContextInfoDTO), JwtUtils.EXPIRATION_TIME);
//        log.error("缓存信息key:{}, value:{}", accessToken, JSONObject.toJSONString(redisUtils.get(accessToken)));
//        Map<String, String> map = new HashMap<>();
//        map.put(JwtUtils.HEADER_STRING, accessToken);
//        return ResultDTO.success(map);
        return null;
    }

    @Override
    public ResultDTO loginOut(HttpServletRequest request) {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return ResultDTO.success("退出成功");
    }
}
