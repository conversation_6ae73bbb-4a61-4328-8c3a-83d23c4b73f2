package com.cosfo.oms.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2024-01-08
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class AuthUseLoginDTO {

    /**
     * 密码是否正确
     */
    private Boolean passwordSuccess;

    /**
     * 当前失败次数
     */
    private Integer errorCount;

    /**
     * 总次数
     */
    private Integer totalCount;

    /**
     * 剩余次数
     */
    private Integer remainCount;

    /**
     * 锁定时间秒
     */
    private Long lockTime;

}
