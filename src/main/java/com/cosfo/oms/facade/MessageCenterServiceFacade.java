package com.cosfo.oms.facade;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.message.client.msgtemplate.provider.MsgTemplateCommandProvider;
import com.cosfo.message.client.msgtemplate.provider.MsgTemplateQueryProvider;
import com.cosfo.message.client.msgtemplate.req.*;
import com.cosfo.message.client.msgtemplate.resp.*;
import com.cosfo.oms.common.context.MsgTemplateEnum;
import com.cosfo.oms.common.utils.AssertParam;
import com.cosfo.oms.common.utils.PageInfoHelper;
import com.cosfo.oms.facade.convert.tenant.TenantInfoMapper;
import com.cosfo.oms.facade.convert.MsgTemplateConverter;
import com.cosfo.oms.facade.dto.tenant.TenantInputQueryDTO;
import com.cosfo.oms.msgscene.service.MsgSceneDomainService;
import com.cosfo.oms.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.oms.tenant.model.po.Tenant;
import com.cosfo.oms.tenant.model.po.TenantAuthConnection;
import com.cosfo.oms.tenant.model.vo.TenantVO;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.cosfo.oms.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.oms.wechat.model.dto.*;
import com.cosfo.oms.wechat.model.po.WechatAuthorizer;
import com.cosfo.oms.wechat.model.vo.RetryTemplateVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/16 8:04
 */
@Slf4j
@Component
public class MessageCenterServiceFacade {
    @DubboReference
    private MsgTemplateQueryProvider msgTemplateQueryProvider;
    @DubboReference
    private MsgTemplateCommandProvider msgTemplateCommandProvider;
    @Resource
    private WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    private TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    private MsgSceneDomainService msgSceneDomainService;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    @Value("${tp.appId}")
    private String platformAppId;

    private Integer pageSize = 10;
    private Integer pageIndex = 1;
    private Long pId = 0L;

    public PageInfo<MsgPublicTemplateWechatDTO> getWechatPublicMsgTemplateList(MsgTemplateWechatPublicReq msgTemplateWechatPublicReq) {
        DubboResponse<PageInfo<MsgPublicTemplateWechatListResultResp>> templateResponse = msgTemplateQueryProvider.getWechatPublicMsgTemplateListByCondition(msgTemplateWechatPublicReq);
        List<MsgPublicTemplateWechatDTO> wechatDTOList = templateResponse.getData().getList().stream().map(MsgTemplateConverter::msgPublicTemplateWechatListResultResp2DTO).collect(Collectors.toList());
        PageInfo<MsgPublicTemplateWechatDTO> pageInfo = (PageInfo<MsgPublicTemplateWechatDTO>) MsgTemplateConverter.turnToPageInfo(new PageInfo<MsgPublicTemplateWechatDTO>(), templateResponse.getData());
        pageInfo.setList(wechatDTOList);
        return pageInfo;
    }

    public PageInfo<MsgTemplateWechatDTO> getWechatMsgTemplateList(WechatMsgTemplateReq wechatMsgTemplateReq) {
        DubboResponse<PageInfo<MsgTemplateWechatListResultResp>> response = msgTemplateQueryProvider.getWechatMsgTemplateListByCondition(wechatMsgTemplateReq);
        List<MsgTemplateWechatListResultResp> wechatListResultRespList = response.getData().getList();
        List<Long> idList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(wechatListResultRespList)) {
            idList = wechatListResultRespList.stream().map(item -> item.getCreator()).collect(Collectors.toList());
        } else {
            PageInfo<MsgTemplateWechatDTO> pageInfo = new PageInfo<>();
            pageInfo.setList(new LinkedList<>());
            pageInfo.setPageNum(wechatMsgTemplateReq.getPageIndex());
            pageInfo.setPageSize(wechatMsgTemplateReq.getPageSize());
            return pageInfo;
        }
        Map<Long, TenantResultResp> tenantVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(idList)) {
            tenantVOMap = tenantInfoService.getTenantInfoMap(idList);
        }
        List<MsgTemplateWechatDTO> wechatDTOList = new LinkedList<>();
        for (MsgTemplateWechatListResultResp resultResp : wechatListResultRespList) {
            wechatDTOList.add(MsgTemplateConverter.msgTemplateWechatListResultResp2DTO(resultResp, tenantVOMap));
        }
        for (MsgTemplateWechatDTO wechatDTO : wechatDTOList) {
            wechatDTO.setSceneCount(msgSceneDomainService.countScene(wechatDTO.getId()));
        }
        PageInfo<MsgTemplateWechatDTO> pageInfo = (PageInfo<MsgTemplateWechatDTO>) MsgTemplateConverter.turnToPageInfo(new PageInfo<MsgTemplateWechatDTO>(), response.getData());
        pageInfo.setList(wechatDTOList);
        return pageInfo;
    }


    public MsgSceneWechatDetailDTO getWechatMsgTemplateDetail(Long id) {
        DubboResponse<MsgTemplateWechatDetailResultResp> response = msgTemplateQueryProvider.getWechatMsgTemplateDetailByPublicTemplateId(id);
        MsgTemplateWechatDetailResultResp resultResp = response.getData();
        MsgSceneWechatDetailDTO wechatDetailDTO = MsgTemplateConverter.msgTemplateWechatDetailResultResp2DTO(resultResp);
        // 设置关联场景总数
        wechatDetailDTO.setSceneCount(msgSceneDomainService.countScene(id));
        return wechatDetailDTO;
    }

    public PageInfo<MsgTemplateWechatDTO> getWechatAppList(MsgWechatAppReq msgWechatAppReq) {
        if (MsgTemplateEnum.CreateStatus.FAIL.getValue().equals(msgWechatAppReq.getSuccessFlag())) {
            return getFailTemplateListByCondition(msgWechatAppReq);
        }
        PageInfo<MsgTemplateWechatDTO> pageInfo = new PageInfo<>();
        // 查询出该模板对应的信息
        DubboResponse<MsgPublicTemplateWechatListResultResp> publicResponse = msgTemplateQueryProvider.getWechatPublicMsgTemplateById(msgWechatAppReq.getId());
        MsgPublicTemplateWechatListResultResp publicTemplateWechatResultResp = publicResponse.getData();
        MsgPublicTemplateWechatDTO msgPublicTemplateWechatDTO = MsgTemplateConverter.msgPublicTemplateWechatListResultResp2DTO(publicTemplateWechatResultResp);
        // 最终需要返回的Tenant集合
        List<Tenant> interList = new ArrayList<>();
        // 最终需要返回的MsgTemplateWechatDTO集合
        List<MsgTemplateWechatDTO> msgTemplateWechatDTOList = new ArrayList<>();


        // 按商城条件查询出的租户信息
        List<Long> tenantIds = tenantInfoService.getTenantIdsByQuery(TenantInputQueryDTO.builder().tenantName(msgWechatAppReq.getMallName()).id(msgWechatAppReq.getMallId()).type(0).build());
        // 没有符合条件的商城，则直接返回空
        if (CollectionUtils.isEmpty(tenantIds)) {
            return pageInfo;
        }
        // 按商城条件对已关联的商城信息进行筛选
        if (!CollectionUtils.isEmpty(tenantIds)) {
            DubboResponse<List<Long>> idResponse = new DubboResponse<>();
            if (ObjectUtils.isNotEmpty(msgWechatAppReq.getAvailableStatus())) {
                idResponse = msgTemplateQueryProvider.getEnableTenantIdListByTenantIdList(msgWechatAppReq.getId(), tenantIds, msgWechatAppReq.getAvailableStatus());
            } else {
                idResponse = msgTemplateQueryProvider.getEnableTenantIdListByTenantIdList(msgWechatAppReq.getId(), tenantIds, null);
            }
            List<Long> idList = idResponse.getData();
            log.info("消息中心返回的list:{}" + JSONObject.toJSONString(idList));
            if (MsgTemplateEnum.MsgSceneStatus.ABLED.getValue().equals(msgWechatAppReq.getType())) {
                if (!CollectionUtils.isEmpty(idList)) {
                    PageInfo<TenantAndBusinessInfoResultResp> respPageInfo = userCenterTenantFacade.getTenantAndCompanyPage(TenantInputQueryDTO.builder().tenantIdList(idList).pageIndex(msgWechatAppReq.getPageIndex()).pageSize(msgWechatAppReq.getPageSize()).build());
                    List<TenantAndBusinessInfoResultResp> tenantVOList = respPageInfo.getList();
                    interList = tenantVOList.stream().map(MsgTemplateConverter::tenantVO2Tenant).collect(Collectors.toList());
                    PageInfo<TenantVO> voPageInfo = TenantInfoMapper.INSTANCE.respPageToVoPage(respPageInfo);
                    pageInfo.setTotal(voPageInfo.getTotal());
                    pageInfo.setPages(voPageInfo.getPages());
                    pageInfo.setPageSize(voPageInfo.getPageSize());
                    pageInfo.setPageNum(voPageInfo.getPageNum());
                }
                List<Long> tenantIdList = interList.stream().map(item -> item.getId()).collect(Collectors.toList());
                // 按查询条件查询后得到的模板对象，用于获取关键词
                if (!CollectionUtils.isEmpty(tenantIdList)) {
                    DubboResponse<List<MsgTemplateToAppResultResp>> wechatListByCondition = msgTemplateQueryProvider.getWechatListByTenantIdList(msgWechatAppReq.getId(), tenantIdList);
                    List<MsgTemplateToAppResultResp> appResultRespList = wechatListByCondition.getData();
                    List<MsgTemplateWechatDTO> wechatDTOList = appResultRespList.stream().map(MsgTemplateConverter::msgTemplateToAppResultResp2DTO).collect(Collectors.toList());
                    Map<Long, MsgTemplateWechatDTO> wechatDTOMap = wechatDTOList.stream().collect(Collectors.toMap(MsgTemplateWechatDTO::getTenantId, item -> item));
                    for (Tenant tenant : interList) {
                        // 封装关键词
                        MsgTemplateWechatDTO msgTemplateWechatDTO = MsgTemplateConverter.tenantVO2MsgTemplateWechatDTO(tenant, msgPublicTemplateWechatDTO, wechatDTOMap);
                        msgTemplateWechatDTOList.add(msgTemplateWechatDTO);
                    }
                }
                pageInfo.setList(msgTemplateWechatDTOList);
            } else {
                tenantIds.removeAll(idList);
                List<TenantAndBusinessInfoResultResp> tenantVOList = new LinkedList<>();
                PageInfo<TenantAndBusinessInfoResultResp> respPageInfo = new PageInfo<>();
                if (!CollectionUtils.isEmpty(tenantIds)) {
                    respPageInfo = userCenterTenantFacade.getTenantAndCompanyPage(TenantInputQueryDTO.builder().tenantIdList(tenantIds).pageIndex(msgWechatAppReq.getPageIndex()).pageSize(msgWechatAppReq.getPageSize()).build());
                    tenantVOList = respPageInfo.getList();
                }
                PageInfo<TenantVO> voPageInfo = TenantInfoMapper.INSTANCE.respPageToVoPage(respPageInfo);
                interList = tenantVOList.stream().map(MsgTemplateConverter::tenantVO2Tenant).collect(Collectors.toList());
                pageInfo.setTotal(voPageInfo.getTotal());
                pageInfo.setPages(voPageInfo.getPages());
                pageInfo.setPageSize(voPageInfo.getPageSize());
                pageInfo.setPageNum(voPageInfo.getPageNum());
                List<Long> unRelateTenantIdList = interList.stream().map(item -> item.getId()).collect(Collectors.toList());
                List<TenantAuthConnection> authConnectionList = new ArrayList<>();
                Map<Long, TenantAuthConnection> authConnectionMap = new HashMap<>();
                Map<String, WechatAuthorizer> authorizerMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(unRelateTenantIdList)) {
                    authConnectionList = tenantAuthConnectionMapper.selectByTenantIdList(unRelateTenantIdList);
                    authConnectionMap = authConnectionList.stream().collect(Collectors.toMap(TenantAuthConnection::getTenantId, item -> item));
                    authorizerMap = wechatAuthorizerMapper.selectListAppIdsAndPlat(authConnectionList.stream().map(item -> item.getAppId()).collect(Collectors.toList()), platformAppId).stream().collect(Collectors.toMap(WechatAuthorizer::getAppId, item -> item));
                }
                for (Tenant tenant : interList) {
                    MsgTemplateWechatDTO msgTemplateWechatDTO = MsgTemplateConverter.tenantVO2MsgTemplateWechatDTO(tenant, authConnectionMap, authorizerMap);
                    if (ObjectUtils.isNotEmpty(msgTemplateWechatDTO)) {
                        msgTemplateWechatDTOList.add(msgTemplateWechatDTO);
                    }
                }
                pageInfo.setList(msgTemplateWechatDTOList);
            }
        }

        return pageInfo;
    }

    public MsgTemplateWechatDTO getWechatAppInfoById(Long id) {
        DubboResponse<MsgTemplateToAppResultResp> response = msgTemplateQueryProvider.getWechatAppInfoByTemplateId(id);
        MsgTemplateToAppResultResp resultResp = response.getData();
        MsgTemplateWechatDTO wechatDTO = MsgTemplateConverter.msgTemplateToAppResultResp2DTO(resultResp);
        TenantResultResp tenantInfo = tenantInfoService.getTenantInfo(wechatDTO.getTenantId());
        if (ObjectUtils.isNotEmpty(tenantInfo)) {
            wechatDTO.setMallName(tenantInfo.getTenantName());
        }
        return wechatDTO;
    }

    public WechatCreateAllTemplateResp createAllTemplateList(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq) {
        // 全部可用小程序信息
        List<TenantAuthConnection> authConnectionList = tenantAuthConnectionMapper.selectAllAuthConnection();
        // 该模板已关联的商城id
        DubboResponse<List<Long>> response = msgTemplateQueryProvider.getRelateIdListByTemplateId(privateMsgTemplateWechatReq.getId());
        // 已关联该模板商城信息
        List<TenantAuthConnection> tenantAuthConnectionRelateList = new ArrayList<>();
        // 未关联该模板商城信息
        List<TenantAuthConnection> nonRelateList = new ArrayList<>();
        List<Long> relateIdList = response.getData();
        Map<String, TenantAuthConnection> authConnectionMap = new HashMap<>();
        if (CollectionUtils.isEmpty(relateIdList)) {
            // 去除没有appId的数据，查询剩下的小程序用户信息并封装
            nonRelateList = authConnectionList.stream().filter(item -> StringUtils.isNotEmpty(item.getAppId())).collect(Collectors.toList());
        } else {
            tenantAuthConnectionRelateList = tenantAuthConnectionMapper.selectByTenantIdList(relateIdList);
            List<Long> idList = tenantAuthConnectionRelateList.stream().map(item -> item.getId()).collect(Collectors.toList());
            // 去除已关联商城id，查询剩下的小程序用户信息并封装
            nonRelateList = authConnectionList.stream()
                    .filter(item -> !idList.contains(item.getId()))
                    .filter(item -> StringUtils.isNotEmpty(item.getAppId()))
                    .collect(Collectors.toList());
        }
        log.info("nonRelateList:{}", JSONObject.toJSONString(nonRelateList));
        authConnectionMap = nonRelateList.stream().collect(Collectors.toMap(TenantAuthConnection::getAppId, item -> item, (v1, v2) -> v1));
        List<WechatAuthorizer> wechatAuthorizerList = wechatAuthorizerMapper.selectListAppIdsAndPlat(nonRelateList.stream().map(item -> item.getAppId()).collect(Collectors.toList()), platformAppId);
        List<WechatAuthorReq> wechatAuthorReqs = new ArrayList<>();
        for (WechatAuthorizer authorizer : wechatAuthorizerList) {
            wechatAuthorReqs.add(MsgTemplateConverter.wechatAuthorizer2Req(authorizer, authConnectionMap));
        }
        privateMsgTemplateWechatReq.setWechatAuthorReqList(wechatAuthorReqs);
        DubboResponse<WechatCreateAllTemplateResp> respDubboResponse = msgTemplateCommandProvider.createAllTemplateList(privateMsgTemplateWechatReq);
        return respDubboResponse.getData();
    }

    public Long createOneTemplate(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq) {
        DubboResponse<MsgPublicTemplateWechatListResultResp> responseTemplate = msgTemplateQueryProvider.getWechatPublicMsgTemplateById(privateMsgTemplateWechatReq.getId());
        if (!pId.equals(responseTemplate.getData().getPId())) {
            TenantAuthConnection authConnection = tenantAuthConnectionMapper.selectByTenantId(privateMsgTemplateWechatReq.getTenantId());
            WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(authConnection.getAppId(), platformAppId);
            List<WechatAuthorizer> wechatAuthorizerList = new ArrayList<>();
            wechatAuthorizerList.add(wechatAuthorizer);
            List<WechatAuthorReq> wechatAuthorReqList = new ArrayList<>();
            for (WechatAuthorizer authorizer : wechatAuthorizerList) {
                wechatAuthorReqList.add(MsgTemplateConverter.wechatAuthorizer2Req(authorizer, null));
            }
            privateMsgTemplateWechatReq.setWechatAuthorReqList(wechatAuthorReqList);
        }
        DubboResponse<Long> response = msgTemplateCommandProvider.createTemplate(privateMsgTemplateWechatReq);
        return response.getData();
    }


    public WechatCreateTemplateResp createTemplateRetry(Long id, Long tenantId) {
        TenantAuthConnection authConnection = tenantAuthConnectionMapper.selectByTenantId(tenantId);
        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(authConnection.getAppId(), platformAppId);
        DubboResponse<WechatCreateTemplateResp> templateRetry = msgTemplateCommandProvider.createTemplateRetry(id, wechatAuthorizer.getAccessToken());
        return templateRetry.getData();
    }

    public MsgPublicTemplateWechatListResultResp getWechatPublicMsgTemplateById(Long id) {
        DubboResponse<MsgPublicTemplateWechatListResultResp> response = msgTemplateQueryProvider.getWechatPublicMsgTemplateById(id);
        MsgPublicTemplateWechatListResultResp resultResp = response.getData();
        return resultResp;
    }

    public List<MsgPublicTemplateWechatListResultResp> getWechatPublicMsgTemplateByIdList(List<Long> idList) {
        DubboResponse<List<MsgPublicTemplateWechatListResultResp>> response = msgTemplateQueryProvider.getWechatPublicMsgTemplateByIdList(idList);
        List<MsgPublicTemplateWechatListResultResp> resultResp = response.getData();
        return resultResp;
    }

    public List<MsgPublicTemplateWechatListResultResp> getWechatListByCondition(List<Long> list) {
        WechatMsgTemplateQueryReq wechatMsgTemplateQueryReq = new WechatMsgTemplateQueryReq();
        wechatMsgTemplateQueryReq.setTemplateIdList(list);
        DubboResponse<List<MsgPublicTemplateWechatListResultResp>> response = msgTemplateQueryProvider.getWechatPublicMsgTemplateByCondition(wechatMsgTemplateQueryReq);
        List<MsgPublicTemplateWechatListResultResp> resultResp = response.getData();
        return resultResp;
    }

    public void refreshTemplate(RetryTemplateVO retryTemplateVO) {
        TenantAuthConnection authConnection = tenantAuthConnectionMapper.selectByTenantId(retryTemplateVO.getTenantId());
        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(authConnection.getAppId(), platformAppId);
        msgTemplateCommandProvider.refreshApp(retryTemplateVO.getId(), wechatAuthorizer.getAccessToken());
    }

    public WechatCreateTemplateResp createOneWeiXinTemplate(PrivateMsgTemplateWechatReq privateMsgTemplateWechatReq) {
        TenantAuthConnection authConnection = tenantAuthConnectionMapper.selectByTenantId(privateMsgTemplateWechatReq.getTenantId());
        if (ObjectUtils.isEmpty(authConnection)) {
            throw AssertParam.buildParamsException(505, "该小程序不符合创建要求");
        }
        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(authConnection.getAppId(), platformAppId);
        List<WechatAuthorizer> wechatAuthorizerList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(wechatAuthorizer)) {
            wechatAuthorizerList.add(wechatAuthorizer);
        } else {
            throw AssertParam.buildParamsException(505, "该小程序不符合创建要求");
        }
        Map<String, TenantAuthConnection> authConnectionMap = new HashMap<>();
        authConnectionMap.put(authConnection.getAppId(), authConnection);
        List<WechatAuthorReq> wechatAuthorReqList = new ArrayList<>();
        for (WechatAuthorizer authorizer : wechatAuthorizerList) {
            wechatAuthorReqList.add(MsgTemplateConverter.wechatAuthorizer2Req(authorizer, authConnectionMap));
        }
        privateMsgTemplateWechatReq.setWechatAuthorReqList(wechatAuthorReqList);
        DubboResponse<WechatCreateTemplateResp> weiXinTemplateResponse = msgTemplateCommandProvider.createWeiXinTemplate(privateMsgTemplateWechatReq);
        return weiXinTemplateResponse.getData();
    }

    /**
     * 获取帆台模板下创建个人消息模板失败小程序列表
     */
    public PageInfo<MsgTemplateWechatDTO> getFailTemplateListByCondition(MsgWechatAppReq msgWechatAppReq) {
        DubboResponse<List<MsgTemplateWechatAppListResultResp>> failResponse = msgTemplateQueryProvider.getFailTemplateListByCondition(msgWechatAppReq);
        List<MsgTemplateWechatAppListResultResp> resultRespList = failResponse.getData();
        // 失败的商城的id集合
        if (CollectionUtils.isEmpty(resultRespList)) {
            return PageInfoHelper.createPageInfo(new ArrayList<>(), msgWechatAppReq.getPageSize());
        }
        List<Long> tenantIdList = resultRespList.stream().map(item -> item.getTenantId()).collect(Collectors.toList());
//        List<TenantVO> tenantVOList = tenantMapper.selectByIds(tenantIdList);
        List<TenantResultResp> tenantInfoList = tenantInfoService.getTenantInfoList(tenantIdList);
        if (ObjectUtils.isNotEmpty(msgWechatAppReq.getMallName())) {
            List<Long> tenantIdFilterByName = tenantInfoService.getTenantIdsByName(msgWechatAppReq.getMallName());
            if (CollectionUtils.isEmpty(tenantIdFilterByName)) {
                return PageInfoHelper.createPageInfo(new ArrayList<>(), msgWechatAppReq.getPageSize());
            }
            // 失败的商城信息和按商城名称查询出的条件进行交集
            tenantInfoList = tenantInfoList.stream().filter(item -> tenantIdFilterByName.contains(item.getId())).collect(Collectors.toList());
        }
        Map<Long, TenantResultResp> tenantVOMap = tenantInfoList.stream().collect(Collectors.toMap(TenantResultResp::getId, item -> item));
        List<MsgTemplateWechatDTO> dtoList = resultRespList.stream().map(MsgTemplateConverter::msgTemplateWechatAppListResultResp2DTO).collect(Collectors.toList());
        Map<Long, MsgTemplateWechatDTO> dtoMap = dtoList.stream().collect(Collectors.toMap(MsgTemplateWechatDTO::getTenantId, item -> item));
        // 按商城名称筛选后的结果
        List<MsgTemplateWechatDTO> wechatDTOList = new LinkedList<>();
        for (Long tenantId : tenantVOMap.keySet()) {
            if (ObjectUtils.isNotEmpty(dtoMap.get(tenantId))) {
                wechatDTOList.add(dtoMap.get(tenantId));
            }
        }
        for (MsgTemplateWechatDTO wechatDTO : wechatDTOList) {
            if (ObjectUtils.isNotEmpty(tenantVOMap.get(wechatDTO.getTenantId()))) {
                wechatDTO.setMallName(tenantVOMap.get(wechatDTO.getTenantId()).getTenantName());
                if (ObjectUtils.isNotEmpty(wechatDTO.getCreator()) && !"null".equals(wechatDTO.getCreator())) {
                    if (ObjectUtils.isNotEmpty(tenantVOMap.get(Long.valueOf(wechatDTO.getCreator())))) {
                        wechatDTO.setCreator(tenantVOMap.get(Long.valueOf(wechatDTO.getCreator())).getTenantName());
                    }
                }
            }
        }
        PageHelper.startPage(msgWechatAppReq.getPageIndex(), msgWechatAppReq.getPageSize());
        PageInfo<MsgTemplateWechatDTO> pageInfo = PageInfoHelper.createPageInfo(wechatDTOList, msgWechatAppReq.getPageSize());
        return pageInfo;
    }
}
