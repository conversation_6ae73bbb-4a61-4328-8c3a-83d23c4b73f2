package com.cosfo.oms.facade.convert;

import com.cosfo.oms.model.vo.CategoryVO;
import com.cosfo.oms.product.model.dto.ProductCategoryDTO;
import net.summerfarm.goods.client.resp.CategoryAllPathResp;
import net.summerfarm.goods.client.resp.CategoryDetailResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/9/27 14:24
 * @Description:
 */
@Mapper
public interface CategoryFacadeConvert {
    CategoryFacadeConvert INSTANCE = Mappers.getMapper(CategoryFacadeConvert.class);

    List<CategoryVO> convert2Vos(List<CategoryDetailResp> resps);

    @Mapping(source = "childrenList", target = "categoryVOS")
    CategoryVO convert2Vo(CategoryDetailResp resp);


    @Mapping(source = "firstCategory", target = "firstCategoryName")
    @Mapping(source = "secondCategory", target = "secondCategoryName")
    @Mapping(source = "thirdCategory", target = "thirdCategoryName")
    ProductCategoryDTO convert2Dto(CategoryAllPathResp resp);
}
