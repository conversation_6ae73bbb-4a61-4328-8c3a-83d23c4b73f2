package com.cosfo.oms.facade;

import com.cofso.page.PageResp;
import com.cofso.preferential.client.enums.ProductSkuPreferentialStatusEnum;
import com.cofso.preferential.client.provider.ProductSkuPreferentialCostPriceProvider;
import com.cofso.preferential.client.req.ProductPreferentialCostPriceCommandReq;
import com.cofso.preferential.client.req.ProductSkuPreferentialDeleteReq;
import com.cofso.preferential.client.req.ProductSkuPreferentialQueryReq;
import com.cofso.preferential.client.resp.ProductSkuPreferentialBasicResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialPageResp;
import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.facade.convert.ProductSkuPreferentialCostPriceConverter;
import com.cosfo.oms.model.vo.LocationProvinceCityVO;
import com.cosfo.oms.product.model.dto.ProductPreferentialCostPriceDTO;
import com.cosfo.oms.product.model.dto.ProductPreferentialCostPriceDeleteDTO;
import com.cosfo.oms.tenant.model.dto.PreferentialCostPriceQueryDTO;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: fansongsong
 * @Date: 2024-02-22
 * @Description:
 */
@Component
public class ProductSkuPreferentialCostPriceFacade {

    @DubboReference
    private ProductSkuPreferentialCostPriceProvider productSkuPreferentialCostPriceProvider;


    /**
     * 分页查询省心订商品列表
     *
     * @param productSkuPreferentialQueryReq
     * @return
     */
    public PageResp<ProductSkuPreferentialPageResp> pagePreferentialCostPrice(ProductSkuPreferentialQueryReq productSkuPreferentialQueryReq) {
        DubboResponse<PageResp<ProductSkuPreferentialPageResp>> response = productSkuPreferentialCostPriceProvider.pagePreferentialCostPrice(productSkuPreferentialQueryReq);
        if (!response.isSuccess()) {
            throw new ProviderException("分页查询省心订商品列表失败，失败原因: " + response.getMsg());
        }
        return response.getData();
    }

    public PageResp<ProductSkuPreferentialPageResp> pagePreferentialCostPriceByParam(PreferentialCostPriceQueryDTO queryDTO, List<Long> skuIds) {
        ProductSkuPreferentialQueryReq queryReq = new ProductSkuPreferentialQueryReq();
        queryReq.setTenantId(queryDTO.getTenantId());
        queryReq.setPageIndex(queryDTO.getPageIndex());
        queryReq.setPageSize(queryDTO.getPageSize());
        queryReq.setSkuIds(skuIds);
        return pagePreferentialCostPrice(queryReq);
    }

    public PageResp<ProductSkuPreferentialPageResp> existValidConfigQuery(Long tenantId) {
        ProductSkuPreferentialQueryReq queryReq = new ProductSkuPreferentialQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setPageIndex(NumberConstants.ZERO);
        queryReq.setPageSize(NumberConstants.ONE);
        queryReq.setStatusEnum(ProductSkuPreferentialStatusEnum.VALID);
        return pagePreferentialCostPrice(queryReq);
    }

    public ProductSkuPreferentialBasicResp queryBasicData(PreferentialCostPriceQueryDTO queryDTO, List<Long> skuIds) {
        ProductSkuPreferentialQueryReq queryReq = new ProductSkuPreferentialQueryReq();
        queryReq.setTenantId(queryDTO.getTenantId());
        queryReq.setSkuIds(skuIds);
        DubboResponse<ProductSkuPreferentialBasicResp> response = productSkuPreferentialCostPriceProvider.queryBasicData(queryReq);
        if (!response.isSuccess()) {
            throw new ProviderException("查询省心订汇总基础数据信息失败，失败原因: " + response.getMsg());
        }
        return response.getData();
    }

    public Boolean deleteSkuPreferentialCostPrice(ProductPreferentialCostPriceDeleteDTO dto) {
        ProductSkuPreferentialDeleteReq deleteReq = new ProductSkuPreferentialDeleteReq();
        deleteReq.setTenantId(dto.getTenantId());
        deleteReq.setSkuId(dto.getSkuId());
        DubboResponse<Boolean> response = productSkuPreferentialCostPriceProvider.deleteSkuPreferentialCostPrice(deleteReq);
        if (!response.isSuccess()) {
            throw new ProviderException("删除租户报价货品省心订信息失败，失败原因: " + response.getMsg());
        }
        return response.getData();
    }

    public Boolean upsertSkuCostPrice(List<ProductPreferentialCostPriceDTO> dtoList, List<LocationProvinceCityVO> locationProvinceCityVOS) {
        Long tenantId = dtoList.get(NumberConstants.ZERO).getTenantId();
        List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq> commandReqList = dtoList.stream().map(costPriceDTO -> {
            ProductPreferentialCostPriceCommandReq.CostPriceCommandReq commandReq = ProductSkuPreferentialCostPriceConverter.INSTANCE.dtoToCommandReq(costPriceDTO);
            List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> cityPriceReqList = ProductSkuPreferentialCostPriceConverter.INSTANCE
                    .cityListToReqList(costPriceDTO.getCityPriceDTOS());
            commandReq.setCityPriceReqList(cityPriceReqList);
            return commandReq;
        }).collect(Collectors.toList());

        List<ProductPreferentialCostPriceCommandReq.CityDataReq> cityDataReqList = ProductSkuPreferentialCostPriceConverter.INSTANCE.cityListToReq(locationProvinceCityVOS);
        ProductPreferentialCostPriceCommandReq productPreferentialCostPriceCommandReq = new ProductPreferentialCostPriceCommandReq();
        productPreferentialCostPriceCommandReq.setTenantId(tenantId);
        productPreferentialCostPriceCommandReq.setCostPriceCommandReqs(commandReqList);
        productPreferentialCostPriceCommandReq.setCityDataReqs(cityDataReqList);
        DubboResponse<Boolean> response = productSkuPreferentialCostPriceProvider.upsertSkuPreferentialCostPrice(productPreferentialCostPriceCommandReq);
        if (!response.isSuccess()) {
            if ("PARAMS-DEFAULT_ERROR".equals(response.getCode())) {
                throw new ParamsException(response.getMsg());
            }
            throw new ProviderException("写入品牌方省心定价格失败，失败原因: " + response.getMsg());
        }
        return response.getData();
    }
}
