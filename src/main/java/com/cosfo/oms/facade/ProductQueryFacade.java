package com.cosfo.oms.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.oms.common.config.GrayReleaseConfig;
import com.cosfo.oms.common.constant.Constants;
import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.common.constant.XianmuSupplyTenant;
import com.cosfo.oms.common.utils.FormatUtils;
import com.cosfo.oms.common.utils.PageInfoHelper;
import com.cosfo.oms.common.utils.StringUtils;
import com.cosfo.oms.facade.convert.ProductFacadeConvert;
import com.cosfo.oms.model.vo.CategoryVO;
import com.cosfo.oms.product.mapper.ProductAgentSkuMappingMapper;
import com.cosfo.oms.product.mapper.ProductSkuMapper;
import com.cosfo.oms.product.mapper.ProductSpuMapper;
import com.cosfo.oms.product.model.dto.ProductPricingSupplyQueryDTO;
import com.cosfo.oms.product.model.dto.ProductSkuDTO;
import com.cosfo.oms.product.model.dto.SupplySkuQueryDTO;
import com.cosfo.oms.product.model.po.ProductAgentSkuMapping;
import com.cosfo.oms.product.model.po.ProductSku;
import com.cosfo.oms.product.model.vo.ProductSpuVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.constant.GoodsConstant;
import net.summerfarm.goods.client.enums.AgentTypeEnum;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.provider.ProductsSpuQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.req.ProductSkuDetailQueryReq;
import net.summerfarm.goods.client.req.ProductSkuListReq;
import net.summerfarm.goods.client.req.ProductSkuPageQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuBaseResp;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/9/21 18:07
 * @Description:
 */
@Slf4j
@Component
public class ProductQueryFacade {
    @Resource
    private GrayReleaseConfig grayReleaseConfig;
    @Resource
    private ProductAgentSkuMappingMapper productAgentSkuMappingMapper;
    @Resource
    private ProductSpuMapper productSpuMapper;
    @Resource
    private ProductSkuMapper productSkuMapper;
    @Resource
    private CategoryServiceFacade categoryServiceFacade;

    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;
    @DubboReference
    private ProductsSpuQueryProvider productsSpuQueryProvider;
    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;


    /**
     * 根据查询条件查询sku mapping
     *
     * @return
     */
    public List<ProductsMappingResp> selectByAgentSkuCode(Long tenantId, String skuCode) {
        if (Objects.isNull(tenantId) || Objects.isNull(skuCode)) {
            return Collections.emptyList();
        }
        if (grayReleaseConfig.isGoodsCenterGrayRelease()) {
            ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setSkuList(Collections.singletonList(skuCode));
            return RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
        } else {
            List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.selectByAgentSkuCode(tenantId, skuCode);
            return ProductFacadeConvert.INSTANCE.convert2MappingDtos(productAgentSkuMappings);
        }
    }

    /**
     * 根据查询条件查询sku mapping
     *
     * @return
     */
    public List<ProductsMappingResp> selectBySkuIds(Long tenantId, List<Long> skuIds) {
        if (Objects.isNull(tenantId) || CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        if (grayReleaseConfig.isGoodsCenterGrayRelease()) {
            ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setSkuIds(skuIds);
            return RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
        } else {
            List<ProductAgentSkuMapping> productAgentSkuMappings = productAgentSkuMappingMapper.selectBySkuIds(tenantId, skuIds);
            return ProductFacadeConvert.INSTANCE.convert2MappingDtos(productAgentSkuMappings);
        }
    }

    /**
     * 根据查询条件查询sku mapping
     *
     * @return
     */
    public ProductsMappingResp selectByTenantIdAndAgentSkuId(Long tenantId, Long agentSkuId) {
        if (Objects.isNull(tenantId) || Objects.isNull(agentSkuId)) {
            return null;
        }
        if (grayReleaseConfig.isGoodsCenterGrayRelease()) {
            ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
            queryReq.setTenantId(tenantId);
            queryReq.setAgentSkuIds(Collections.singletonList(agentSkuId));
            List<ProductsMappingResp> productsMappingResps = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
            if (CollectionUtils.isEmpty(productsMappingResps)) {
                return null;
            } else {
                return productsMappingResps.get(0);
            }
        } else {
            ProductAgentSkuMapping productAgentSkuMapping = productAgentSkuMappingMapper.selectByTenantIdAndAgentSkuId(tenantId, agentSkuId);
            return ProductFacadeConvert.INSTANCE.convert2MappingDto(productAgentSkuMapping);
        }
    }


    /**
     * 根据供应商SKUID，查询sku信息
     *
     * @param skuIds
     * @param isNeedCategory
     * @return
     */
    public List<ProductSpuVO> queryBySupplySkuIds(List<Long> skuIds, Boolean isNeedCategory, Boolean isNeedSkuMapping) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        ProductSkuPageQueryReq queryReq = new ProductSkuPageQueryReq();
        queryReq.setTenantId(XianmuSupplyTenant.TENANT_ID);
        queryReq.setSkuIds(skuIds);
        queryReq.setIsNeedAllPathCategory(isNeedCategory);
        queryReq.setNotInSubAgentTypeList(Constants.NOT_IN_SUB_AGENT_TYPE_LIST);
        return selectSkuList(queryReq);
    }


    /**
     * 获取所有供应商skuID 列表
     *
     * @param queryDTO
     * @return
     */
    public List<Long> queryPricingSupplyList(ProductPricingSupplyQueryDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            return Collections.emptyList();
        }
        ProductSkuPageQueryReq queryReq = new ProductSkuPageQueryReq();
        queryReq.setTenantId(XianmuSupplyTenant.TENANT_ID);
        queryReq.setTitle(queryDTO.getTitle());
        queryReq.setBrandName(queryDTO.getBrandName());
        queryReq.setCategoryId(queryDTO.getCategoryId());
        queryReq.setSkuIds (queryDTO.getSupplySkuIds ());
        Optional.ofNullable(queryDTO.getSubAgentType()).ifPresent(sku -> queryReq.setSubAgentTypeList(Collections.singletonList (queryDTO.getSubAgentType ())));
        Optional.ofNullable(queryDTO.getSku()).ifPresent(sku -> queryReq.setSkuList(Collections.singletonList(sku)));
        List<ProductSpuVO> spuVOS = selectSkuList(queryReq);
        return spuVOS.stream().map(ProductSpuVO::getSkuId).collect(Collectors.toList());
    }

    /**
     * 查询所有供应商sku列表
     *
     * @param queryDTO
     * @param tenantIds
     * @return
     */
    public PageInfo<ProductSpuVO> queryBySupplySkuQueryDTO(SupplySkuQueryDTO queryDTO, List<Long> tenantIds) {
        if (CollectionUtils.isEmpty (tenantIds)) {
            return PageInfoHelper.createPageInfo (Collections.emptyList (), queryDTO.getPageSize ());
        }
        ProductSkuPageQueryReq queryReq = new ProductSkuPageQueryReq ();
        queryReq.setTenantId (XianmuSupplyTenant.TENANT_ID);
        queryReq.setAgentType (AgentTypeEnum.SELF_SUPPORT.getType ());
        queryReq.setTitle (queryDTO.getTitle ());
        Optional.ofNullable (queryDTO.getSku ()).ifPresent (sku -> queryReq.setSkuList (Collections.singletonList (sku)));
        Optional.ofNullable (queryDTO.getSkuId ()).ifPresent (skuId -> queryReq.setSkuIds (Collections.singletonList (skuId)));
        queryReq.setCategoryId (queryDTO.getCategoryId ());
        queryReq.setIsNeedAllPathCategory (queryDTO.getIsNeedAllPathCategory ());
        queryReq.setNotInSubAgentTypeList (Constants.NOT_IN_SUB_AGENT_TYPE_LIST);
        Optional.ofNullable(queryDTO.getSubAgentType()).ifPresent(sku -> queryReq.setSubAgentTypeList(Collections.singletonList (queryDTO.getSubAgentType ())));
        queryReq.setPageIndex(queryDTO.getPageNum());
        queryReq.setPageSize(queryDTO.getPageSize());
        return selectSkuPage(queryReq);
    }



    /**
     * 分页查询SKU列表
     * @param queryReq
     * @return
     */
    private PageInfo<ProductSpuVO> selectSkuPage(ProductSkuPageQueryReq queryReq) {
        if (Objects.isNull(queryReq.getPageIndex())) {
            queryReq.setPageIndex(1);
        }
        if (Objects.isNull(queryReq.getPageSize())) {
            queryReq.setPageSize(GoodsConstant.MAX_SIZE_FOR_PAGE_SEARCH);
        }
        PageInfo<ProductSkuDetailResp> handler = RpcResponseUtil.handler(productsSkuQueryProvider.selectSkuPage(queryReq));
        return ProductFacadeConvert.INSTANCE.convert2SpuVOPage(handler);
    }

    /**
     * 不分页，查询所有SKu列表
     * @param queryReq
     * @return
     */
    private List<ProductSpuVO> selectSkuList(ProductSkuPageQueryReq queryReq) {
        int pageIndex = 1;
        List<ProductSpuVO> spuVOS = new ArrayList<>();
        // 没有分页参数查询所有
        while (true) {
            queryReq.setPageIndex(pageIndex++);
            queryReq.setPageSize(GoodsConstant.MAX_SIZE_FOR_PAGE_SEARCH);
            PageInfo<ProductSkuDetailResp> handler = RpcResponseUtil.handler(productsSkuQueryProvider.selectSkuPage(queryReq));
            if (CollectionUtils.isNotEmpty(handler.getList())) {
                spuVOS.addAll(ProductFacadeConvert.INSTANCE.convert2SpuVOs(handler.getList()));
            }
            if (!handler.isHasNextPage()) {
                break;
            }
        }
        return spuVOS;
    }

    /**
     * 不分页，查询所有SKu列表
     *
     * @param queryReq
     * @return
     */
    public List<ProductSkuDetailResp> selectProductSkuList(ProductSkuPageQueryReq queryReq) {
        int pageIndex = 1;
        List<ProductSkuDetailResp> spuVOS = new ArrayList<>();
        // 没有分页参数查询所有
        while (true) {
            if (pageIndex > NumberConstants.ONE_HUNDRED) {
                log.error("selectSkuPage分页查询获取信息过多，请优化查询功能", new Exception("快速失败"));
                return spuVOS;
            }
            queryReq.setPageIndex(pageIndex++);
            queryReq.setPageSize(GoodsConstant.MAX_SIZE_FOR_PAGE_SEARCH);
            PageInfo<ProductSkuDetailResp> handler = RpcResponseUtil.handler(productsSkuQueryProvider.selectSkuPage(queryReq));
            if (CollectionUtils.isNotEmpty(handler.getList())) {
                spuVOS.addAll(handler.getList());
            }
            if (!handler.isHasNextPage()) {
                break;
            }
        }
        return spuVOS;
    }

    /**
     * 通过SKU查询
     *
     * @param skus
     * @return
     */
    public List<ProductSkuDTO> selectBySkus(List<String> skus,Long tenantId) {
        if (CollectionUtil.isEmpty (skus)) {
            return null;
        }

        List<ProductSkuBaseResp> list = Lists.newArrayList();

        List<List<String>> skuList = Lists.partition(skus, GoodsConstant.MAX_SIZE_FOR_LIST_SEARCH);
        for (List<String> skuTempList : skuList) {
            ProductSkuListReq queryReq = new ProductSkuListReq();
            queryReq.setTenantId(tenantId);
            queryReq.setSkuList(skuTempList);
            List<ProductSkuBaseResp> resps = RpcResponseUtil.handler(productsSkuQueryProvider.selectProductSkuBaseList(queryReq));
            list.addAll(resps);
        }
        List<ProductSkuBaseResp> result = list.stream ().filter (e -> !Constants.NOT_IN_SUB_AGENT_TYPE_LIST.contains (e.getSubAgentType ()) && !AgentTypeEnum.AGENT.getType ().equals (e.getAgentType ())).collect (Collectors.toList ());
        if (CollectionUtil.isNotEmpty (result)){
            return ProductFacadeConvert.INSTANCE.convert2SkuDtos(result);
        }
        return Collections.emptyList ();
    }

    public List<ProductSkuDetailResp> selectProductSkuDetailById(List<Long> skuIds) {
        List<ProductSkuDetailResp> data = RpcResponseUtil.handler(productsSkuQueryProvider.selectProductSkuDetailById(skuIds));
        if (CollectionUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data;
    }
}
