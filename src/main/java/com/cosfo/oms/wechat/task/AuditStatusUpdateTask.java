package com.cosfo.oms.wechat.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.oms.common.context.VersionStatus;
import com.cosfo.oms.common.dingtalk.DingtalkApi;
import com.cosfo.oms.wechat.mapper.WechatTemplatePackageMapper;
import com.cosfo.oms.wechat.model.po.WechatTemplatePackage;
import com.cosfo.oms.wechat.service.WeixinTemplateService;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AuditStatusUpdateTask extends XianMuJavaProcessorV2 implements InitializingBean {

    @Autowired
    private AuditStatusUpdateTask statusUpdateTask;

    @Value("${dingtalk.access.token:947d58cfb53e4ba654c28fb54273fff171067912f4213f2954f58e3df332067f}")
    private String token;

    @Autowired
    private WeixinTemplateService weixinTemplateService;

    @Autowired
    private WechatTemplatePackageMapper templatePackageMapper;

    @Override
    public void afterPropertiesSet() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setJobName("stated right after service startup");
        ProcessResult result = statusUpdateTask.processResult(xmJobInput);

        log.info("update result:{}", JSON.toJSONString(result));
    }

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("about to start update mini-app audit status update...:{}",
                context.getJobParameters());
        String params = context.getJobParameters();
        // params 是appId，逗号分割；
        List<WechatTemplatePackage> updatedApps = new LinkedList<>();
        if (StringUtils.isBlank(params)) {
            updatedApps = weixinTemplateService.syncAuditStatus(null);
        } else {
            for (String appId : params.split(",")) {
                List subResult = weixinTemplateService.syncAuditStatus(appId);
                if (null != subResult) {
                    updatedApps.addAll(subResult);
                }
            }
        }
        if (CollectionUtils.isEmpty(updatedApps)) {
            log.info("no update");
            return new ProcessResult(true);
        }
        // send notice;
        List<Long> updatedIdList = updatedApps.stream().map(WechatTemplatePackage::getId)
                .collect(Collectors.toList());
        updatedApps = templatePackageMapper.listByIds(updatedIdList);
        if (CollectionUtils.isEmpty(updatedApps)) {
            return new ProcessResult(true);
        }
        String noticeContent = String.join("\n", updatedApps.stream().map(app -> {
            VersionStatus versionStatus = VersionStatus.fromCode(app.getPkgStatus());
            return String.join(" : ", app.getAppid(), versionStatus.toString(), app.getPkgDesc());
        }).collect(Collectors.toList()));

        log.warn("notice:{}", noticeContent);
        noticeContent = String.format("收到新的小程序审核结果：\n%s\n\n通知时间：%s", noticeContent,
                new Date());
        boolean result = DingtalkApi.sendTextMessage(token, noticeContent);
        return new ProcessResult(result, noticeContent);
    }
}
