package com.cosfo.oms.wechat.bean.wxa;

import lombok.Data;

import java.io.Serializable;

@Data
public class WxTemplateDTO implements Serializable {

    /**
     * 开发者上传草稿时间戳
     */
    private String create_time;

    /**
     * 版本号，开发者自定义字段
     */
    private String user_version;

    /**
     * 版本描述 开发者自定义字段
     */
    private String user_desc;

    /**
     * 草稿 id
     */
    private Integer draft_id;

    /**
     * 模板 id
     */
    private Integer template_id;

    /**
     * 开发小程序的名称
     */
    private String source_miniprogram;
}
