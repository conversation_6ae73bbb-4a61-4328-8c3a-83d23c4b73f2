package com.cosfo.oms.wechat.bean.wxa;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.oms.wechat.bean.BaseResult;
import lombok.Data;

@Data
public class GetAuditstatusResult extends BaseResult {

    private Integer status;

    // 当status=1，审核被拒绝时，返回的拒绝原因
    private String reason;

    //审核ID
    private String auditid;

    // 当status=1，审核被拒绝时，会返回审核失败的小程序截图示例。
    // xxx丨yyy丨zzz是media_id可通过获取永久素材接口 拉取截图内容）
    private String screenshot;

    @JSONField(name = "user_desc")
    private String userDesc;

    @JSONField(name = "user_version")
    private String userVersion;

    @JSONField(name = "submit_audit_time")
    private Long submitAuditTime;
}
