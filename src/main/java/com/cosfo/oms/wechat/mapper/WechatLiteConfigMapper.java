package com.cosfo.oms.wechat.mapper;

import com.cosfo.oms.wechat.model.po.WechatLiteConfig;
import com.cosfo.oms.wechat.model.vo.AuthorizedVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WechatLiteConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WechatLiteConfig record);

    int insertSelective(WechatLiteConfig record);

    WechatLiteConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WechatLiteConfig record);

    int updateByPrimaryKey(WechatLiteConfig record);

    WechatLiteConfig selectByAppId(@Param("appId") String appId);

    WechatLiteConfig selectByOriginalId(@Param("OriginalId") String OriginalId);

    List<AuthorizedVo> selectAuthorizedList(@Param("tenantId") Long tenantId);


}