package com.cosfo.oms.wechat.service;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.oms.wechat.model.po.WechatAuthorizer;

import javax.servlet.http.HttpServletRequest;
import java.net.URISyntaxException;
import java.util.Map;

public interface WeixinTpService {
//
//    /**
//     * 获取微信ticket
//     * 此接口为绑定微信推送url
//     */
//    String processAuthEvent(WechatAuthorizerDto wechatAuthorizerDto, Map<String, String> params, String postBody);
//
//    /**
//     * 授权变更通知推送（弃用）
//     */
//    String tenantAuthEvent(WechatAuthorizerDto wechatAuthorizerDto, Map<String, String> params, String postBody);
//
//    /**
//     * 创建授权地址
//     */
//    ResultDTO createPreAuthUrl(Long tenantId, HttpServletRequest request) throws URISyntaxException;
//
//    /**
//     * 授权过来的公号修改归属店铺
//     */
//    void updateAuthorizerTenant(String authCode, Long tenantId);
//
//    /**
//     * 处理微信消息
//     *
//     * @param appId
//     * @param params
//     * @param body
//     */
//    String processMessage(String appId, Map<String, String> params, String body);
//
//    /**
//     * 解密微信消息
//     *
//     * @param authorizerDto
//     * @param msgSignature
//     * @param timestamp
//     * @param nonce
//     * @param body
//     * @return
//     */
//    String decodeMessage(WechatAuthorizerDto authorizerDto, WechatAuthorizer wechatAuthorizer, String msgSignature, String timestamp, String nonce, String body);
}
