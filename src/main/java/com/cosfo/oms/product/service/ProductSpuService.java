package com.cosfo.oms.product.service;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/9
 */
public interface ProductSpuService {

    /**
     * 校验类目下是否绑定有商品
     *
     * @param categoryIds
     * @return
     */
    boolean verifyCategoryIsHasBindSpu(List<Long> categoryIds);

    /**
     * 校验品牌下是否绑定有商品
     *
     * @param brandId
     * @return
     */
    boolean verifyBrandIsHasBindSpu(Long brandId);
}
