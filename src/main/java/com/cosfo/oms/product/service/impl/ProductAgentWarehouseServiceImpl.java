package com.cosfo.oms.product.service.impl;

import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.common.constant.XianmuSupplyTenant;
import com.cosfo.oms.common.context.WarehouseTypeEnum;
import com.cosfo.oms.facade.RsWarehouseStorageTenantFacade;
import com.cosfo.oms.facade.SummerFarmInterfaceServiceFacade;
import com.cosfo.oms.facade.WarehouseStorageQueryFacade;
import com.cosfo.oms.product.mapper.ProductAgentWarehouseMapper;
import com.cosfo.oms.product.model.dto.ProductAgentWarehouseDTO;
import com.cosfo.oms.product.model.dto.WarehouseInfoDTO;
import com.cosfo.oms.product.model.po.ProductAgentWarehouse;
import com.cosfo.oms.product.model.vo.ProductAgentWarehouseVO;
import com.cosfo.oms.product.model.vo.WarehouseInfoVO;
import com.cosfo.oms.product.service.ProductAgentWarehouseService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.client.saas.resp.SummerFarmWarehouseInfoResp;
import net.summerfarm.wnc.client.req.WarehouseListQuery;
import net.summerfarm.wnc.client.resp.WarehouseStorageDetailResp;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/27
 */
@Slf4j
@Service
public class ProductAgentWarehouseServiceImpl implements ProductAgentWarehouseService {

//    @Resource
//    private ProductAgentWarehouseMapper productAgentWarehouseMapper;
    @Resource
    private SummerFarmInterfaceServiceFacade summerFarmInterfaceServiceFacade;
    @Resource
    private RsWarehouseStorageTenantFacade rsWarehouseStorageTenantFacade;
    @Resource
    private WarehouseStorageQueryFacade warehouseStorageQueryFacade;

    @Override
    public List<ProductAgentWarehouseVO> queryByTenantId(Long tenantId) {
        return rsWarehouseStorageTenantFacade.queryWarehouseStorageTenant(tenantId);
    }

    @Override
    public List<WarehouseInfoVO> warehouseList() {
        List<WarehouseInfoVO> warehouseInfoVOS = new ArrayList<>();
        try {
            WarehouseListQuery warehouseListQuery = new WarehouseListQuery();
            warehouseListQuery.setTenantId(XianmuSupplyTenant.TENANT_ID);
            // 开放状态：1、开放
            warehouseListQuery.setStatus(1);
            List<WarehouseStorageDetailResp> respList = warehouseStorageQueryFacade.queryWarehouseList(warehouseListQuery);
            warehouseInfoVOS = respList.stream().map(item -> {
                WarehouseInfoVO warehouseInfoVO = new WarehouseInfoVO();
                warehouseInfoVO.setId(item.getWarehouseNo());
                warehouseInfoVO.setName(item.getWarehouseName());
                return warehouseInfoVO;
            }).collect(Collectors.toList());
        }catch (Exception e){
            log.error("获取鲜沐仓库列表信息失败", e);
        }

        return warehouseInfoVOS;
    }

    @Override
    public void addWarehouse(ProductAgentWarehouseDTO productAgentWarehouseDTO) {
        Long tenantId = productAgentWarehouseDTO.getTenantId();
        List<WarehouseInfoDTO> warehouseInfoDTOS = productAgentWarehouseDTO.getWarehouseInfoDTOS();

        List<ProductAgentWarehouseVO> productAgentWarehouseVOS = rsWarehouseStorageTenantFacade.queryWarehouseStorageTenant(tenantId);
        if(CollectionUtils.isEmpty(warehouseInfoDTOS)){
            productAgentWarehouseVOS.forEach(productAgentWarehouseVO -> {
                rsWarehouseStorageTenantFacade.deleteWarehouseStorageTenant(productAgentWarehouseVO.getTenantId(), productAgentWarehouseVO.getWarehouseId());
            });
        }

        // 需要绑定的仓库Id
        List<Long> needAddWarehouseIds = warehouseInfoDTOS.stream().map(WarehouseInfoDTO::getId).collect(Collectors.toList());
        List<Long> bindWarehouseIds = productAgentWarehouseVOS.stream().map(ProductAgentWarehouseVO::getWarehouseId).collect(Collectors.toList());
        // 需要删除的仓库Id

        List<Long> deleteWarehouseIds = productAgentWarehouseVOS.stream().filter(item -> !needAddWarehouseIds.contains(item.getWarehouseId())).map(ProductAgentWarehouseVO::getWarehouseId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteWarehouseIds)) {
            deleteWarehouseIds.forEach(warehouseId -> {
                rsWarehouseStorageTenantFacade.deleteWarehouseStorageTenant(tenantId, warehouseId);
            });
        }

        // 需要新增的仓库Id
        // 新增仓库
        needAddWarehouseIds.forEach(item -> {
            if(!bindWarehouseIds.contains(item)) {
                rsWarehouseStorageTenantFacade.insertWarehouseStorageTenant(tenantId, item);
            }
        });
    }
}
