package com.cosfo.oms.product.model.dto;

import lombok.Data;
import org.apache.tomcat.jni.Local;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/15
 */
@Data
public class BatchUpdateExpireTimeDTO {
    /**
     * 有效开始时间
     */
    private LocalDateTime startTime;
    /**
     * 有效结束时间
     */
    private LocalDateTime endTime;
    /**
     * 城市报价单Id
     */
    private List<Long> citySupplyPriceIds;
}
