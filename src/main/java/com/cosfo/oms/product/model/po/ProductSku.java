package com.cosfo.oms.product.model.po;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * product_sku
 *
 * <AUTHOR>
@Data
public class ProductSku implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * spu id
     */
    private Long spuId;

    /**
     * sku编码
     */
    private String sku;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 货品二级性质(自营/代仓)，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-经销'
     */
    private Integer subAgentType;


    private static final long serialVersionUID = 1L;
}