package com.cosfo.oms.product.mapper;

import com.cosfo.oms.product.model.po.ProductAgentSkuMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductAgentSkuMappingMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ProductAgentSkuMapping record);

    int insertSelective(ProductAgentSkuMapping record);

    ProductAgentSkuMapping selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProductAgentSkuMapping record);

    int updateByPrimaryKey(ProductAgentSkuMapping record);

    /**
     * 查询品牌方代仓sku信息
     *
     * @return
     */
    List<ProductAgentSkuMapping> selectTenantAgentSkuInfo(@Param("tenantId") Long tenantId);

    /**
     * 根据租户Id和代仓skuId
     *
     * @param tenantId
     * @param agentSkuId
     * @return
     */
    ProductAgentSkuMapping selectByTenantIdAndAgentSkuId(@Param("tenantId") Long tenantId,
                                                         @Param("agentSkuId") Long agentSkuId);

    /**
     * 批量查詢
     *
     * @param tenantId
     * @param skuIds
     * @return
     */
    List<ProductAgentSkuMapping> selectBySkuIds(@Param("tenantId") Long tenantId,
                                               @Param("skuIds") List<Long> skuIds);

    /**
     * 根据鲜沐sku查询
     *
     * @return
     */
    List<ProductAgentSkuMapping> selectByAgentSkuCode(@Param("tenantId")Long tenantId,
                                                      @Param("agentSkuCode") String agentSkuCode);

    ProductAgentSkuMapping selectByAgentSkuIdAndAgentTenantIdAndTenantId(@Param("agentTenantId") Long agentTenantId, @Param("agentSkuId") Long agentSkuId, @Param("tenantId") Long tenantId);

}