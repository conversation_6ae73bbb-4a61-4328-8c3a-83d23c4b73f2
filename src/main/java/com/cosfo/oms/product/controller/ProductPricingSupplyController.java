package com.cosfo.oms.product.controller;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.product.model.dto.*;
import com.cosfo.oms.product.model.vo.ProductPricingSupplyVO;
import com.cosfo.oms.product.model.vo.SummerfarmSkuPriceVO;
import com.cosfo.oms.product.service.ProductPricingSupplyService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 报价单管理V2
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/9
 */
@Slf4j
@RestController
@RequestMapping("pricing/supply")
public class ProductPricingSupplyController {

    @Resource
    private ProductPricingSupplyService productPricingSupplyService;

    /**
     * 新增报价单V2
     *
     * @param productPricingSupplyAddNewDTO
     * @return
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public ResultDTO add(@RequestBody ProductPricingSupplyAddNewDTO productPricingSupplyAddNewDTO) {
        return productPricingSupplyService.add(productPricingSupplyAddNewDTO);
    }

    /**
     * 修改报价单
     *
     * @param productPricingSupplyDTO
     * @return
     */
    @RequestMapping(value = "edit", method = RequestMethod.POST)
    public ResultDTO edit(@RequestBody ProductPricingSupplyDTO productPricingSupplyDTO) {
        return productPricingSupplyService.edit(productPricingSupplyDTO);
    }

    /**
     * 删除报价单
     *
     * @param tenantId
     * @param citySupplyPriceId
     * @return
     */
    @RequestMapping(value = "remove", method = RequestMethod.DELETE)
    public ResultDTO remove(Long tenantId, Long citySupplyPriceId) {
        return productPricingSupplyService.remove(tenantId, citySupplyPriceId);
    }
    /**
     * 批量删除报价单
     *
     * @param dto
     * @return
     */
    @RequestMapping(value = "batchRemove", method = RequestMethod.POST)
    public ResultDTO batchRemove(@RequestBody @Valid BatchRemoveSupplyPriceDTO dto) {
        return productPricingSupplyService.batchRemove(dto.getCitySupplyPriceIds ());
    }

    /**
     * 报价单列表
     *
     * @return
     */
    @RequestMapping(value = "list/{pageNum}/{pageSize}", method = RequestMethod.POST)
    public ResultDTO<PageInfo<ProductPricingSupplyVO>> list(@RequestBody ProductPricingSupplyQueryDTO productPricingSupplyQueryDTO, @PathVariable Integer pageNum, @PathVariable Integer pageSize) {
        productPricingSupplyQueryDTO.setPageNum(pageNum);
        productPricingSupplyQueryDTO.setPageSize(pageSize);
        long startTime = System.currentTimeMillis();
        ResultDTO<PageInfo<ProductPricingSupplyVO>> list = productPricingSupplyService.list(productPricingSupplyQueryDTO);
        long endTime = System.currentTimeMillis();
        log.info("类目列表数据查询结束时间戳：{}, 数据库查询耗时： {}", endTime, endTime - startTime);
        return list;
    }

    /**
     * 查询未报价的供应商商品列表
     *
     * @param supplySkuQueryDTO
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Deprecated
    @RequestMapping(value = "querySupplySkuInfo/{pageNum}/{pageSize}", method = RequestMethod.POST)
    public ResultDTO querySupplySkuInfo(@RequestBody SupplySkuQueryDTO supplySkuQueryDTO, @PathVariable Integer pageNum, @PathVariable Integer pageSize) {
        supplySkuQueryDTO.setPageNum(pageNum);
        supplySkuQueryDTO.setPageSize(pageSize);
        return productPricingSupplyService.querySupplySkuInfo(supplySkuQueryDTO);
    }

    /**
     * 查询供应商Sku推荐价
     *
     * @param skuIds
     * @return
     */
    @RequestMapping(value = "querySupplySkuRecommendPrice", method = RequestMethod.POST)
    public ResultDTO querySupplySkuRecommendPrice(@RequestBody List<Long> skuIds) {
        return productPricingSupplyService.querySupplySkuRecommendPrice(skuIds);
    }

    /**
     * 城市报价单详情
     *
     * @param citySupplyPriceId
     * @return
     */
    @RequestMapping(value = "detail", method = RequestMethod.GET)
    public ResultDTO detail(Long citySupplyPriceId) {
        return productPricingSupplyService.detail(citySupplyPriceId);
    }

    /**
     * 批量更改指定价
     *
     * @param batchUpdateSupplyPriceDTO
     * @return
     */
    @PostMapping("/upsert/batch-update-supply-price")
    public CommonResult batchUpdateSupplyPrice(@RequestBody BatchUpdateSupplyPriceDTO batchUpdateSupplyPriceDTO) {
        return productPricingSupplyService.batchUpdateSupplyPrice(batchUpdateSupplyPriceDTO);
    }

    /**
     * 批量更改有效时间
     *
     * @param batchUpdateExpireTimeDTO
     * @return
     */
    @PostMapping("/upsert/batch-update-expire-time")
    public CommonResult batchUpdateExpireTime(@RequestBody BatchUpdateExpireTimeDTO batchUpdateExpireTimeDTO) {
        return productPricingSupplyService.batchUpdateExpireTime(batchUpdateExpireTimeDTO);
    }

    /**
     * 报价单导入
     *
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/import")
    public CommonResult importProductPricingSupply(@RequestParam("file") MultipartFile file) throws IOException {
        return productPricingSupplyService.importProductPricingSupply(file);
    }

    /**
     * 导出报价单
     *
     * @param productPricingSupplyExportDTO
     * @return
     */
    @PostMapping("/export")
    public CommonResult exportProductPricingSupply(@RequestBody ProductPricingSupplyExportDTO productPricingSupplyExportDTO) {
        return productPricingSupplyService.exportProductPricingSupply(productPricingSupplyExportDTO);
    }

    /**
     * 随鲜沐同价查询鲜沐价格
     *
     * @param summerfarmSkuPriceDTO
     * @return
     */
    @PostMapping("/query/summerfarm-price")
    public CommonResult<SummerfarmSkuPriceVO> querySummerfarmPrice(@RequestBody SummerfarmSkuPriceDTO summerfarmSkuPriceDTO) {
        return productPricingSupplyService.querySummerfarmPrice(summerfarmSkuPriceDTO);
    }
    /**
     * 查询未报价的供应商商品列表
     *
     * @param supplySkuQueryDTO
     * @return
     */
    @RequestMapping(value = "querySupplySkuInfo", method = RequestMethod.POST)
    public ResultDTO querySupplySkuInfoNew(@RequestBody SupplySkuQueryDTO supplySkuQueryDTO) {
        supplySkuQueryDTO.setPageNum(supplySkuQueryDTO.getPageIndex());
        return productPricingSupplyService.querySupplySkuInfo(supplySkuQueryDTO);
    }
}
