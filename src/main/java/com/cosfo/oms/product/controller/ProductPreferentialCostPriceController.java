package com.cosfo.oms.product.controller;

import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.product.model.dto.ProductPreferentialCostPriceDTO;
import com.cosfo.oms.product.model.dto.ProductPreferentialCostPriceDeleteDTO;
import com.cosfo.oms.product.model.vo.ProductPreferentialCostPriceBasicDataVO;
import com.cosfo.oms.product.model.vo.ProductPreferentialCostPriceVO;
import com.cosfo.oms.product.service.ProductPreferentialCostPriceService;
import com.cosfo.oms.tenant.model.dto.PreferentialCostPriceQueryDTO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 省心定 详情
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "preferential_cost_price")
public class ProductPreferentialCostPriceController extends BaseController {

    @Resource
    private ProductPreferentialCostPriceService productPreferentialCostPriceService;

    /**
     * 查询品牌方省心定价格列表
     *
     * @return
     */
    @PostMapping("/query/list")
    public CommonResult<PageInfo<ProductPreferentialCostPriceVO>> listPreferentialCostPrice(@Valid @RequestBody PreferentialCostPriceQueryDTO queryDTO) {
        PageInfo<ProductPreferentialCostPriceVO> pageInfo = productPreferentialCostPriceService.listPreferentialCostPrice(queryDTO);
        return CommonResult.ok(pageInfo);
    }

    /**
     * 查询品牌方省心定 基础数据
     *
     * @return
     */
    @PostMapping("/query/basic_data")
    public CommonResult<ProductPreferentialCostPriceBasicDataVO> queryBasicData(@Valid @RequestBody PreferentialCostPriceQueryDTO queryDTO) {
        ProductPreferentialCostPriceBasicDataVO basicDataVO = productPreferentialCostPriceService.queryBasicData(queryDTO);
        return CommonResult.ok(basicDataVO);
    }

    /**
     * 新增品牌方省心定价格
     *
     * @return
     */
    @PostMapping("/upsert")
    public CommonResult insert(@RequestBody @Valid List<ProductPreferentialCostPriceDTO> dtoList) {
        productPreferentialCostPriceService.upsertSkuCostPrice(dtoList);
        return CommonResult.ok();
    }

    /**
     * 删除品牌方省心定价格
     *
     * @return
     */
    @PostMapping("/delete")
    public CommonResult delete(@RequestBody @Valid ProductPreferentialCostPriceDeleteDTO dto) {
        Boolean result = productPreferentialCostPriceService.deleteSkuPreferentialCostPrice(dto);
        return CommonResult.ok(result);
    }

    /**
     * 租户下是否存在有效状态的货品省心订配置
     *
     * @return true:存在,false:不存在
     */
    @PostMapping("exist-valid-config/query")
    public CommonResult existValidConfig(@RequestBody ProductPreferentialCostPriceDeleteDTO dto) {
        Boolean result = productPreferentialCostPriceService.existValidConfigQuery(dto.getTenantId());
        return CommonResult.ok(result);
    }
}
