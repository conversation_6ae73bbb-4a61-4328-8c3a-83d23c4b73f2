package com.cosfo.oms.product.controller;

import com.cosfo.oms.product.model.dto.ProductAgentWarehouseDTO;
import com.cosfo.oms.product.model.po.ProductAgentWarehouse;
import com.cosfo.oms.product.model.vo.ProductAgentWarehouseVO;
import com.cosfo.oms.product.model.vo.WarehouseInfoVO;
import com.cosfo.oms.product.service.ProductAgentWarehouseService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 品牌方代理仓库
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/27
 */
@RestController
@RequestMapping("/product/agent/warehouse")
public class ProductAgentWarehouseController {
    @Resource
    private ProductAgentWarehouseService productAgentWarehouseService;

    /**
     * 租户绑定仓库列表
     *
     * @param tenantId
     * @return
     */
    @PostMapping("/list")
    public CommonResult<List<ProductAgentWarehouseVO>> list(Long tenantId){
        List<ProductAgentWarehouseVO> productAgentWarehouseVOS = productAgentWarehouseService.queryByTenantId(tenantId);
        return CommonResult.ok(productAgentWarehouseVOS);
    }

    /**
     * 添加仓库列表
     *
     * @param productAgentWarehouseDTO
     * @return
     */
    @PostMapping("/add/warehouse")
    public CommonResult addWarehouse(@RequestBody ProductAgentWarehouseDTO productAgentWarehouseDTO){
        productAgentWarehouseService.addWarehouse(productAgentWarehouseDTO);
        return CommonResult.ok(Boolean.TRUE);
    }

    /**
     * 仓库列表
     *
     * @return
     */
    @PostMapping("/warehouse/list")
    public CommonResult<List<WarehouseInfoVO>> warehouseList(){
        List<WarehouseInfoVO> warehouseInfoVOS = productAgentWarehouseService.warehouseList();
        return CommonResult.ok(warehouseInfoVOS);
    }
}
