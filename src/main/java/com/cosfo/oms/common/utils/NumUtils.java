package com.cosfo.oms.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

public class NumUtils {

    private static final Logger log = LoggerFactory.getLogger(NumUtils.class);

    public NumUtils() {
    }

    public static boolean isEqual(Integer n1, Integer n2) {
        if (null == n1) {
            return null == n2;
        } else {
            return null == n2 ? false : n1.equals(n2);
        }
    }

    public static boolean isEqual(Long n1, Long n2) {
        if (null == n1) {
            return null == n2;
        } else {
            return null == n2 ? false : n1.equals(n2);
        }
    }

    public static Integer toInteger(String value) {
        if (!StringUtils.isNumeric(value)) {
            return null;
        } else {
            try {
                return Integer.valueOf(value);
            } catch (Exception var2) {
                log.error(var2.getMessage(), var2);
                return null;
            }
        }
    }

    public static Long toLong(String value) {
        if (!StringUtils.isNumeric(value)) {
            return null;
        } else {
            try {
                return Long.valueOf(value);
            } catch (Exception var2) {
                log.error(var2.getMessage(), var2);
                return null;
            }
        }
    }

    public static Long toLong(Integer value) {
        if (value == null) {
            return null;
        } else {
            try {
                return (long) value;
            } catch (Exception var2) {
                log.error(var2.getMessage(), var2);
                return null;
            }
        }
    }

    public static BigDecimal toBigDecimal(String value) {
        try {
            return new BigDecimal(value);
        } catch (Exception var2) {
            log.error(var2.getMessage(), var2);
            return null;
        }
    }
}
