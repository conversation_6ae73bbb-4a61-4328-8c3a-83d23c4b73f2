package com.cosfo.oms.common.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/6  11:11
 */
@Data
@NoArgsConstructor
public class ResultDTO<D> implements Serializable {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应messag
     */
    private String message;

    /**
     * 返回数据
     */
    private D data;

    public ResultDTO(ResultDTOEnum resultDtoEnum) {
        this.code = resultDtoEnum.getCode();
        this.message = resultDtoEnum.getMessage();
    }

    public ResultDTO(ResultDTOEnum resultDtoEnum, D data) {
        this.code = resultDtoEnum.getCode();
        this.message = resultDtoEnum.getMessage();
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public D getData() {
        return data;
    }

    public void setData(D data) {
        this.data = data;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return this.code.equals(ResultDTOEnum.SUCCESS.getCode());
    }

    @JsonIgnore
    public boolean isFail() {
        return !isSuccess();
    }

    public static <D> ResultDTO<D> success(D data) {
        ResultDTO<D> resultDTO = new ResultDTO<>(ResultDTOEnum.SUCCESS);
        resultDTO.setData(data);
        return resultDTO;
    }

    public static <D> ResultDTO<D> success() {
        return new ResultDTO<>(ResultDTOEnum.SUCCESS);
    }

    public static <D> ResultDTO<D> fail(ResultDTOEnum enumCode) {
        return new ResultDTO<>(enumCode);
    }

    public static <D> ResultDTO<D> fail(String msg) {
        ResultDTO<D> resultDTO = new ResultDTO<>();
        resultDTO.setCode(ResultDTOEnum.SERVER_ERROR.getCode());
        resultDTO.setMessage(msg);
        return resultDTO;
    }

    public static <D> ResultDTO<D> fail(Integer code, String msg) {
        ResultDTO<D> resultDTO = new ResultDTO<>();
        resultDTO.setCode(code);
        resultDTO.setMessage(msg);
        return resultDTO;
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
