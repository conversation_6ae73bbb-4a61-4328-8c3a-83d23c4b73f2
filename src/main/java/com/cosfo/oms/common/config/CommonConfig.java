package com.cosfo.oms.common.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: fansongsong
 * @Date: 2024-01-08
 * @Description:常规的公共配置
 */
@Component
@Data
@Slf4j
public class CommonConfig {


    @NacosValue(value = "${change.password.interval.days:180}", autoRefreshed = true)
    private Integer changePasswordIntervalDays;

    /**
     * 是否禁用密码登录
     */
    @NacosValue(value = "${password.login.enable:true}", autoRefreshed = true)
    private boolean passwordLoginEnable;

    /**
     * 发送短信间隔时间
     */
    @NacosValue(value = "${sms.send.interval.time:60000}", autoRefreshed = true)
    private Long smsSendIntervalTime;

    /**
     * 超级账号token过去时间，默认单位h
     */
    @NacosValue(value = "${admin.token.expire.time:4}", autoRefreshed = true)
    private Long superTokenExpireTime;

    /**
     * 关闭使用手机号一键登录的阈值（正常配置0，做成可配置的方便测试）
     */
    @NacosValue(value = "${close.use.phone.one-click.login.threshold:0}", autoRefreshed = true)
    private Integer closeUsePhoneOneClickLoginThreshold;

    @NacosValue(value = "${store.address.audit.detail.url:https://boss.cosfo.cn/index.html#/customer/poi-change/detail?auditId=}", autoRefreshed = true)
    private String storeAddressAuditDetailUrl;

    @NacosValue(value = "${store.address.audit.notify:[{\"feiShuUrl\":\"https://open.feishu.cn/open-apis/bot/v2/hook/35926634-7c00-4b57-b0d6-d59d2287c692\",\"sign\":\"63bpWvTtx3SrLgn9fTXbng\"}]}", autoRefreshed = true)
    private String storeAddressAuditNotify;


    @Data
    public static class StoreAddressAuditNotify {
        /**
         * 飞书机器人url
         */
        private String feiShuUrl;
        /**
         * 签名
         */
        private String sign;
    }

    @NacosConfigListener(dataId = "${spring.application.name}")
    public void onConfigChanged(String newConfig) {
        log.info("Nacos配置已更新:{}", newConfig);
    }
}
