package com.cosfo.oms.common.executor;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.arms.sdk.v1.async.TraceExecutors;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池工厂
 * <AUTHOR>
 */
public class ExecutorFactory {
    private static ExecutorService generateExcelExecutor0 = new ThreadPoolExecutor(1, 2,
            60L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(100), new NamedThreadFactory("exportExcelExecutor", false),
            new ThreadPoolExecutor.AbortPolicy());


    public static ExecutorService generateExcelExecutor = TraceExecutors.wrapExecutorService(generateExcelExecutor0, true);
}
