package com.cosfo.oms.common.context;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: monna.chen
 * @Date: 2023/4/11 16:22
 * @Description: 货源类型
 */
@Getter
@AllArgsConstructor
public enum GoodsTypeEnum {

    /**
     * 无货商品
     */
    NO_GOOD_TYPE(0, "无货商品"),
    /**
     * 报价商品
     */
    QUOTATION_TYPE(1, "报价商品"),
    /**
     * 自营货品
     */
    SELF_GOOD_TYPE(2, "自营货品");

    private Integer code;
    private String desc;

    public static GoodsTypeEnum getTypeByCode(Integer code) {
        for (GoodsTypeEnum type : GoodsTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }

        return null;
    }

    public static String getDescByCode(Integer code) {
        for (GoodsTypeEnum type : GoodsTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type.getDesc();
            }
        }

        return null;
    }

    public static final Set<Integer> NO_WAREHOUSE_CODES = new HashSet<>(Lists.newArrayList(NO_GOOD_TYPE.getCode(),SELF_GOOD_TYPE.getCode()));

    public static final Set<Integer> THIRD_DELIVERY_CODES = new HashSet<>(Lists.newArrayList(SELF_GOOD_TYPE.getCode(),QUOTATION_TYPE.getCode()));

    public static Integer getThreePartiesCode() {
        return QUOTATION_TYPE.getCode();
    }

    public static Integer getBrandDeliveryCode() {
        return NO_GOOD_TYPE.getCode();
    }


}
