package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/4
 */
@Getter
@AllArgsConstructor
public enum BillSwitchEnum {
    /**
     * 开启
     */
    OPEN(1,"开启"),

    /**
     * 关闭
      */
    SHUTDOWN(0,"关闭");
    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * get desc
     * @param type
     * @return
     */
    public static String getDesc(Integer type) {
        for (BillSwitchEnum value : BillSwitchEnum.values()) {
            if (Objects.equals(value.getCode(), type)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
