package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2023/1/5 19:09
 * 卡的类型
 */
@Getter
@AllArgsConstructor
public enum CardTypeEnum {
    PUBLIC("0","对公"),
    PRIVATE("1","对私");

    private String code;
    private String desc;

    /**
     * 状态描述
     * @param code
     * @return
     */
    public static String getDesc(String code) {
        for (CardTypeEnum cardTypeEnum : CardTypeEnum.values()) {
            if (Objects.equals(code, cardTypeEnum.getCode())) {
                return cardTypeEnum.getDesc();
            }
        }
        return null;
    }
}
