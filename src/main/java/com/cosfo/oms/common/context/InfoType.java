package com.cosfo.oms.common.context;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/31
 */
@Getter
@AllArgsConstructor
public enum InfoType {
    /**
     * 三方平台票据
     */
    COMPONENT_VERIFY_TICKET("component_verify_ticket", "三方平台票据"),
    /**
     * 取消授权
     */
    UN_AUTHORIZED("unauthorized", "取消授权"),
    /**
     * 授权成功
     */
    AUTHORIZED("authorized", "授权成功"),
    /**
     * 更新授权
     */
    UPDATE_AUTHORIZED("updateauthorized", "更新授权");

    /**
     * 状态类型编码
     */
    private String str;
    /**
     * 状态类型描述
     */
    private String desc;
}
