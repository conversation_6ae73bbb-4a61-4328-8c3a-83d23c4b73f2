package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/26 20:57
 */
@Getter
@AllArgsConstructor
public enum FileDownloadStatusEnum {

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 处理完毕
     */
    FINISHED(2, "处理完毕"),

    /**
     * 处理失败
     */
    FAIL(3, "处理失败");

    private Integer status;

    private String desc;
}
