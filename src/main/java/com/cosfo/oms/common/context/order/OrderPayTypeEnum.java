package com.cosfo.oms.common.context.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/8/9
 */
@Getter
@AllArgsConstructor
public enum OrderPayTypeEnum {
    /**
     * 线上支付
     */
    ONLINE_PAY(1, "微信支付"),
    /**
     * 账期支付
     */
    BILL(2, "账期支付"),

    /**
     * 余额支付
     */
    BALANCE(3, "余额支付"),

    /**
     * 支付宝
     */
    ALI_PAY(4, "支付宝"),

    /**
     * 无需支付
     */
    ZERO_PRICE_PAY(5, "无需支付"),
    /**
     * 线下支付
     */
    OFFLINE_PAY(6, "线下支付"),
    ;
    /**
     * 状态类型编码
     */
    private Integer type;
    /**
     * 状态类型描述
     */
    private String desc;

    private static final Map<Integer, String> TYPE_TO_DESC_MAP = new HashMap<>();

    static {
        for (OrderPayTypeEnum payTypeEnum : OrderPayTypeEnum.values()) {
            TYPE_TO_DESC_MAP.put(payTypeEnum.type, payTypeEnum.desc);
        }
    }

    /**
     * 获取支付方式描述
     * @param payType
     * @return
     */
    public static String getDesc(Integer payType) {
        return TYPE_TO_DESC_MAP.getOrDefault(payType, ONLINE_PAY.desc);
    }
}
