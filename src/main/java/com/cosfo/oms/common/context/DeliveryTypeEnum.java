package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/26 14:16
 */
@Getter
@AllArgsConstructor
public enum DeliveryTypeEnum {

    /**
     * 配送
     */
    DELIVERY(0, "配送"),

    /**
     * 回收
     */
    RECYCLE(1, "回收"),

    /**
     * 换货
     */
    EXCHANGE(2, "换货");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;
}
