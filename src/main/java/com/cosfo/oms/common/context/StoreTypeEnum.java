package com.cosfo.oms.common.context;

import com.alibaba.excel.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述: 门店类型枚举
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/20
 */
@Getter
@AllArgsConstructor
public enum StoreTypeEnum {
    //直营店，加盟店，托管店
    DIRECT(0, "直营店"),
    FRANCHISE(1, "加盟店"),
    HOSTING(2, "托管店"),
    /**
     * 3-个人店
     */
    PERSONAL(3, "个人店"),
    /**
     * 4-连锁店
     */
    CHAIN(4, "连锁店"),
    /**
     * 5-未知
     */
    UN_KNOW(5, "未知");
    /**
     * 门店类型编码
     */
    private Integer code;
    /**
     * 门店类型描述
     */
    private String desc;

    public static String getDesc(Integer code) {
        for (StoreTypeEnum storeTypeEnum : StoreTypeEnum.values()) {
            if (storeTypeEnum.code.equals(code)) {
                return storeTypeEnum.desc;
            }
        }

        return StringUtils.EMPTY;
    }
}
