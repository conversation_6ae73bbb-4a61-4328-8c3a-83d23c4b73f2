package com.cosfo.oms.common.context;

import lombok.Getter;
import net.summerfarm.payment.routing.common.enums.PaymentDictionaryEnums;
import net.summerfarm.payment.routing.common.enums.PaymentMethodEnums;


/**
 * @description: 支付场景枚举
 * @author: Gemini
 * @date: 2025-08-18
 */
@Getter
public enum PaymentSceneEnum {

    // --- 微信支付场景 ---
    APPLET_WECHAT_PAY("小程序微信支付", PaymentDictionaryEnums.Platform.MINI_APP.getName(), PaymentMethodEnums.WECHAT.getCode()),
    H5_WECHAT_PAY("H5微信支付", PaymentDictionaryEnums.Platform.H5.getName(), PaymentMethodEnums.WECHAT.getCode()),

    // --- 支付宝支付场景 ---
    H5_ALI_PAY("H5支付宝支付", PaymentDictionaryEnums.Platform.H5.getName(), PaymentMethodEnums.ALIPAY.getCode());

    private final String sceneName;
    private final String platform;
    private final String paymentMethod;

    PaymentSceneEnum(String sceneName, String platform, String paymentMethod) {
        this.sceneName = sceneName;
        this.platform = platform;
        this.paymentMethod = paymentMethod;
    }
}
