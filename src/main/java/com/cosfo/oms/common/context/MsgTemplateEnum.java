package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date : 2023/2/16 16:31
 */
@Getter
@AllArgsConstructor
public class MsgTemplateEnum {
    /**
     * 是否关联模板
     */
    @AllArgsConstructor
    @Getter
    public enum MsgSceneStatus{
        DISABLED(0,"未关联"),
        ABLED(1,"关联");

        private Integer value;
        private String content;
    }

    /**
     * 创建成功/失败
     */
    @AllArgsConstructor
    @Getter
    public enum CreateStatus{
        FAIL(0,"失败"),
        SUCCESS(1,"成功");

        private Integer value;
        private String content;
    }
    /**
     * 模板类型
     */
    @AllArgsConstructor
    @Getter
    public enum TemplateType{
        WECHAT(1,"微信");

        private Integer value;
        private String content;

    }
    /**
     * 模板类型
     */
    @AllArgsConstructor
    @Getter
    public enum AvailableStatus{
        DISABLED(0,"已删除"),
        ABLED(1,"生效中");

        private Integer value;
        private String content;
    }


    /**
     * 是否成功响应
     */
    @AllArgsConstructor
    @Getter
    public enum WechatErrorCode{
        SUCCESS(0,"成功");

        private Integer value;
        private String content;

    }
}
