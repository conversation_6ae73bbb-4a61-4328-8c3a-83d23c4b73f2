package com.cosfo.oms.common.context;

/**
 * 描述: Excel枚举类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/19
 */
public enum ExcelTypeEnum {
    /**
     * 报价单导出表
     */
    PRODUCT_PRICING_SUPPLY_EXPORT(1, "product_pricing_supply.xlsx", "导出报价单表"),

    /**
     * 报价单导入表
     */
    PRODUCT_PRICING_SUPPLY_IMPORT_ERROR(2, "product_pricing_supply_import_template.xlsx", "导入报价单错误信息模板"),

    /**
     * 报价单导入模板
     */
    PRODUCT_PRICING_SUPPLY_IMPORT(3, "file/报价单导入模板.xlsx", "报价单导入模板"),


    PREPAYMENT_RECORD_EXPORT(5,"prepayment_record_export.xlsx", "预付概况导出信息"),
    PREPAYMENT_TRANSACTION_EXPORT(6,"prepayment_transaction_export.xlsx", "收支明细导出信息"),
    PREPAYMENT_ACCOUNT_EXPORT(7,"prepayment_account_export.xlsx", "余额构成导出信息"),
    ORDER_EXPORT(8,"boss_order.xlsx", "订单导出信息"),
    /**
     * 售后单
     */
    AFTER_SALE_ORDER_EXPORT(9,"boss_after_sale_order.xlsx","售后订单列表"),
    /**
     * 品牌门店列表导出
     */
    MERCHANT_STORE_EXPORT(10,"merchant_store.xlsx","品牌门店列表导出"),

    ERROR_STORE_FULFILLMENT(11,"error_store_fulfillment.xlsx","批量更新门店履约信息导入错误信息"),

    IMPORT_BOSS_STORE_FULFILLMENT(12,"","批量更新门店履约模版"),

    STORE_ADDRESS_AUDIT_EXPORT(13,"store_address_audit.xlsx","门店审核列表导出"),
    ;

    private Integer order;
    private String name;
    private String desc;

    ExcelTypeEnum(Integer order, String name, String desc) {
        this.order = order;
        this.name = name;
        this.desc = desc;
    }

    public Integer getOrder() {
        return this.order;
    }

    public String getName() {
        return this.name;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getNameByOrder(Integer order) {
        for (ExcelTypeEnum excelTypeEnum : ExcelTypeEnum.values()) {
            if (excelTypeEnum.order.equals(order)) {
                return excelTypeEnum.name;
            }
        }

        return null;
    }

    public static ExcelTypeEnum getByOrder(Integer order) {
        for (ExcelTypeEnum excelTypeEnum : ExcelTypeEnum.values()) {
            if (excelTypeEnum.order.equals(order)) {
                return excelTypeEnum;
            }
        }

        return null;
    }
}
