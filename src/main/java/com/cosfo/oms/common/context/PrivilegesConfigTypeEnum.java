package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PrivilegesConfigTypeEnum {

    /**
     * 销售版本
     */
    SALE_VERSION(0, "销售版本"),

    /**
     * 增购项目
     */
    ADD_FUNCTION(1, "增购项目");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;
}

