package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/24
 */
@Getter
@AllArgsConstructor
public enum HuifuAccountTypeEnum {
    /**
     * 企业
     */
    PROPRIETARY("1","MERCHANT_ID"),
    /**
     * 个人
     */
    THREE_PARTIES("2","PERSONAL_OPENID");

    /**
     * 类型编码
     */
    private String code;
    /**
     * 类型描述
     */
    private String desc;

    public static HuifuAccountTypeEnum getByCode(String code) {
        for (HuifuAccountTypeEnum huifuAccountTypeEnum : HuifuAccountTypeEnum.values()) {
            if (code.equals(huifuAccountTypeEnum.getCode())) {
                return huifuAccountTypeEnum;
            }
        }

        return null;
    }
}
