package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/16
 */
@AllArgsConstructor
@Getter
public enum SupplyStatusEnum {
    /**
     * 在售
     */
    HAVING_INVENTORY(0, "有库存"),
    /**
     * 未在售
     */
    NOT_HAVING_INVENTORY(1, "库存不足");

    private Integer code;
    private String desc;

    public static SupplyStatusEnum getByCode(Integer code) {
        for (SupplyStatusEnum supplyStatusEnum : SupplyStatusEnum.values()) {
            if (supplyStatusEnum.code.equals(code)) {
                return supplyStatusEnum;
            }
        }

        return null;
    }
}
