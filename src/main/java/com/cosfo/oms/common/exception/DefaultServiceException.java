package com.cosfo.oms.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.exception.BizException;

/**
 * 2023.2.27 该异常已经废弃，请使用 net.xianmu.common.exception 下相关异常类
 * <AUTHOR>
 * @date 2022/5/6  14:25
 */
@Deprecated
@EqualsAndHashCode(callSuper = true)
@Data
public class DefaultServiceException extends BizException {
    /**
     * 异常编码枚举
     */
    private Integer code;

    /**
     * 异常描述
     */
    private String message;

    public DefaultServiceException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public DefaultServiceException(String message) {
        super(message);
        this.message = message;
    }

    public DefaultServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public DefaultServiceException(Throwable cause) {
        super(cause);
    }

}
