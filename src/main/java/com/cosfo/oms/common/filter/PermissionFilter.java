package com.cosfo.oms.common.filter;

import com.alibaba.fastjson.JSON;
import net.xianmu.authentication.common.utils.SpringContextUtil;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.shiro.web.filter.authc.FormAuthenticationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/20
 */
public class PermissionFilter extends FormAuthenticationFilter {
    public PermissionFilter() {
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        return request instanceof HttpServletRequest && ((HttpServletRequest) request).getMethod().toUpperCase().equals("OPTIONS") ? true : super.isAccessAllowed(request, response, mappedValue);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        httpServletResponse.setHeader("Access-Control-Allow-Origin", ((HttpServletRequest) request).getHeader("Origin"));
        httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/json");
        String requestURI = ((HttpServletRequest) request).getRequestURI();
        if (requestURI.equals("/")) {
            this.saveRequestAndRedirectToLogin(request, response);
            return false;
        } else {
            String loginUrl = this.getLoginUrl();
            if (loginUrl.indexOf(requestURI) == -1 && !requestURI.equals("/favicon.ico")) {
                RedisTemplate redisTemplate = (RedisTemplate) SpringContextUtil.getBean("authRedisTemplate", RedisTemplate.class);
                String token = WebUtils.toHttp(request).getHeader("token");
                String tipMsg = "LOGIN_FIRST";
                String redisKey = String.format("auth:kick_out:session:%s", token);
                if (Boolean.TRUE.equals(redisTemplate.delete(redisKey))) {
                    tipMsg = "REPLACE_LOGIN";
                }

                CommonResult error = CommonResult.fail(ResultStatusEnum.UNAUTHORIZED, ResultStatusEnum.UNAUTHORIZED.getMsg(), ResultStatusEnum.UNAUTHORIZED.getStatus().toString());
                httpServletResponse.setStatus(401);
                httpServletResponse.getWriter().write(JSON.toJSONString(error));
                return false;
            } else {
                return true;
            }
        }
    }

    @Override
    protected void redirectToLogin(ServletRequest request, ServletResponse response) throws IOException {
        String loginUrl = this.getLoginUrl();
        WebUtils.issueRedirect(request, response, loginUrl, (Map) null, true, false);
    }

}