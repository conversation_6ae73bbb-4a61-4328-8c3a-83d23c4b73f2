<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.order.mapper.OrderItemMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.oms.order.model.po.OrderItem">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="item_id" jdbcType="BIGINT" property="itemId"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="payable_price" jdbcType="DECIMAL" property="payablePrice"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="after_sale_expiry_time" property="afterSaleExpiryTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , tenant_id, order_id, item_id, amount, payable_price, total_price, `status`, create_time,
        update_time, after_sale_expiry_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_item
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from order_item
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.oms.order.model.po.OrderItem"
            useGeneratedKeys="true">
        insert into order_item (tenant_id, order_id, item_id,
        amount, payable_price, total_price,
        `status`, create_time, update_time)
        values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT},
        #{amount,jdbcType=INTEGER}, #{payablePrice,jdbcType=DECIMAL}, #{totalPrice,jdbcType=DECIMAL},
        #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.order.model.po.OrderItem" useGeneratedKeys="true">
        insert into order_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="itemId != null">
                item_id,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="payablePrice != null">
                payable_price,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="itemId != null">
                #{itemId,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=INTEGER},
            </if>
            <if test="payablePrice != null">
                #{payablePrice,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.oms.order.model.po.OrderItem">
        update order_item
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="itemId != null">
                item_id = #{itemId,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
            <if test="payablePrice != null">
                payable_price = #{payablePrice,jdbcType=DECIMAL},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="afterSaleExpiryTime != null">
                after_sale_expiry_time = #{afterSaleExpiryTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.order.model.po.OrderItem">
        update order_item
        set tenant_id = #{tenantId,jdbcType=BIGINT},
        order_id = #{orderId,jdbcType=BIGINT},
        item_id = #{itemId,jdbcType=BIGINT},
        amount = #{amount,jdbcType=INTEGER},
        payable_price = #{payablePrice,jdbcType=DECIMAL},
        total_price = #{totalPrice,jdbcType=DECIMAL},
        `status` = #{status,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_item
        where
        tenant_id = #{tenantId}
        and order_id = #{orderId}
    </select>

    <select id="queryOrderItemVOByOrderId" resultType="com.cosfo.oms.order.model.vo.OrderItemVO">
        select i.id,
        i.item_id itemId,
        i.amount,
        i.item_id skuId,
        i.payable_price price,
        i.total_price totalPrice,
        s.supplier_tenant_id supplierTenantId,
        s.title,
        s.main_picture mainPicture,
        s.specification,
        s.warehouse_type warehouseType,
        s.warehouse_type warehouseType,
        s.delivery_type deliveryType,
        s.goods_type goodsType,
        i.order_id orderId,
        s.supplier_name supplierName,
        s.specification_unit specificationUnit,
        s.after_sale_unit afterSaleUnit,
        i.after_sale_expiry_time afterSaleExpiryTime
        from order_item i
        left join order_item_snapshot s on i.id = s.order_item_id
        where i.order_id = #{orderId}
    </select>

</mapper>
