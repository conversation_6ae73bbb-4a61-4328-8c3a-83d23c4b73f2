<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.bill.mapper.BillProfitSharingOrderMapper">
    <resultMap id="BaseResultMap" type="com.cosfo.oms.bill.model.po.BillProfitSharingOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_id, order_id, `status`, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_profit_sharing_order
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from bill_profit_sharing_order
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.bill.model.po.BillProfitSharingOrder" useGeneratedKeys="true">
        insert into bill_profit_sharing_order (tenant_id, order_id, `status`,
        create_time, update_time)
        values (#{tenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.cosfo.oms.bill.model.po.BillProfitSharingOrder" useGeneratedKeys="true">
        insert into bill_profit_sharing_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.oms.bill.model.po.BillProfitSharingOrder">
        update bill_profit_sharing_order
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.bill.model.po.BillProfitSharingOrder">
        update bill_profit_sharing_order
        set tenant_id = #{tenantId,jdbcType=BIGINT},
        order_id = #{orderId,jdbcType=BIGINT},
        `status` = #{status,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="queryByOrderIdAndTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from
        bill_profit_sharing_order
        where tenant_id = #{tenantId}
        and order_id = #{orderId}
    </select>

    <select id="queryByTenantAndOrderAndSupplierId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from bill_profit_sharing_order
        <where>
            tenant_id = #{tenantId}
            and order_id = #{orderId}
            <if test="supplierId != null">
                and supplier_id = #{supplierId}
            </if>
        </where>
    </select>

</mapper>