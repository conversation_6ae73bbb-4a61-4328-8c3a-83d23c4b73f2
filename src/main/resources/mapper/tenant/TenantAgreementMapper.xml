<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.tenant.mapper.TenantAgreementMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.oms.tenant.model.po.TenantAgreement">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="agreement" jdbcType="VARCHAR" property="agreement" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `type`, agreement, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tenant_agreement
    where id = #{id,jdbcType=INTEGER}
  </select>
    <select id="selectByType" resultType="com.cosfo.oms.tenant.model.po.TenantAgreement">
      select <include refid="Base_Column_List"/>
      from tenant_agreement
      where type = #{type}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from tenant_agreement
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.oms.tenant.model.po.TenantAgreement" useGeneratedKeys="true">
    insert into tenant_agreement (`type`, agreement, create_time,
      update_time)
    values (#{type,jdbcType=TINYINT}, #{agreement,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.oms.tenant.model.po.TenantAgreement" useGeneratedKeys="true">
    insert into tenant_agreement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        `type`,
      </if>
      <if test="agreement != null">
        agreement,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="agreement != null">
        #{agreement,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.oms.tenant.model.po.TenantAgreement">
    update tenant_agreement
    <set>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="agreement != null">
        agreement = #{agreement,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.oms.tenant.model.po.TenantAgreement">
    update tenant_agreement
    set `type` = #{type,jdbcType=TINYINT},
      agreement = #{agreement,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
