<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.tenant.mapper.TenantPrivilegesConfigMapper">
<select id="funcSetRefCnt" resultType="com.cosfo.oms.tenant.model.dto.FuncSetCntDTO">
    SELECT function_set_id as functionSetId,COUNT(*) as quoteCnt
    FROM tenant_privileges_config
    WHERE function_set_id in
    <foreach collection="setIds" item="setId" open="(" separator="," close=")">
        #{setId}
    </foreach>

    group by function_set_id
</select>
</mapper>
