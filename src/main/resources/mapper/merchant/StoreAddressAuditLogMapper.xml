<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.oms.merchant.mapper.StoreAddressAuditLogMapper">

    <resultMap id="StoreAddressAuditLogResultMap" type="com.cosfo.oms.merchant.model.po.StoreAddressAuditLog">
        <id property="id" column="id" />
        <result property="tenantId" column="tenant_id" />
        <result property="storeId" column="store_id" />
        <result property="province" column="province" />
        <result property="city" column="city" />
        <result property="area" column="area" />
        <result property="address" column="address" />
        <result property="houseNumber" column="house_number" />
        <result property="poiNote" column="poi_note" />
        <result property="modifyProvince" column="modify_province" />
        <result property="modifyCity" column="modify_city" />
        <result property="modifyArea" column="modify_area" />
        <result property="modifyAddress" column="modify_address" />
        <result property="modifyHouseNumber" column="modify_house_number" />
        <result property="modifyPoiNote" column="modify_poi_note" />
        <result property="auditRemark" column="audit_remark" />
        <result property="auditStatus" column="audit_status" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="contactName" column="contact_name" />
        <result property="contactPhone" column="contact_phone" />
        <result property="distance" column="distance" />
    </resultMap>

    <sql id="tableName">store_address_audit_log</sql>

    <select id="queryTenantIdsByTime" resultType="java.lang.Long">
        SELECT distinct tenant_id FROM
        <include refid="tableName"/>
        WHERE create_time between #{startOfDay} and #{endOfDay}
    </select>

    <select id="queryByAuditNo" resultMap="StoreAddressAuditLogResultMap">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE audit_no = #{auditNo}
    </select>

    <select id="queryWaitStatusLogByStoreId" resultMap="StoreAddressAuditLogResultMap">
        SELECT *
        FROM
        <include refid="tableName"/>
        WHERE tenant_id = #{tenantId} and store_id = #{storeId} and audit_status = #{auditStatus}
        limit 1
    </select>
</mapper>
