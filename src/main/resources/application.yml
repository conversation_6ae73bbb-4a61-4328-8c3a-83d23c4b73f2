spring:
  schedulerx2:
    enabled: true
  application:
    name: cosfo-oms
  profiles:
    active: dev
logging:
  level:
    root: info
    org.springframework: INFO
    org.mybatis: INFO
    com.cosfo.oms: INFO
  pattern:
    console: "%d - %msg%n"
server:
  port: 8090
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
#pagehelper分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: false  # 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
  supportMethodsArguments: true
  params: count=countSql

root:
  pageSize: 10

# 七牛云配置
qiniu:
  ACCESS_KEY: D7qgaXDWTIv0ormcO8IozO1RyT7P1YLP-y3tObIJ
  SECRET_KEY: ZLVTkrc7cwZlyNhLVieyeBwJ-UD_0p4Co9ZdSu3T
  bucketname: cosfo

xm:
  log:
    enable: true