package com.cosfo.oms.tenant.dao;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class TenantPrepaymentAccountDaoTest {

    @Resource
    private TenantPrepaymentAccountDao accountDao;

    @Test
    void updateAmount() {
        accountDao.updateAmount(1L, BigDecimal.valueOf(10));
    }

    @Test
    public void localAmount() {
        accountDao.lockAmount(3L, BigDecimal.valueOf(10));
    }
}