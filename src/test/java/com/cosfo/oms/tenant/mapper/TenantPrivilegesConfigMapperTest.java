package com.cosfo.oms.tenant.mapper;

import com.alibaba.fastjson.JSON;
import com.cosfo.oms.tenant.model.dto.FuncSetCntDTO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantPrivilegesConfigMapperTest {

    @Resource
    private TenantPrivilegesConfigMapper tenantPrivilegesConfigMapper;

    @Test
    void funcSetRefCnt() {
        List<FuncSetCntDTO> longIntegerMap = tenantPrivilegesConfigMapper.funcSetRefCnt(Lists.newArrayList(100L));
        System.out.println(JSON.toJSONString(longIntegerMap));
    }
}