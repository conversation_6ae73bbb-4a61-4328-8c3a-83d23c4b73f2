package com.cosfo.oms.tenant.service.impl;

import com.cosfo.oms.common.context.TenantTypeEnum;
import com.cosfo.oms.tenant.service.TenantInfoService;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class TenantInfoServiceImplTest {

    @Resource
    private TenantInfoService tenantInfoService;

    @Test
    void getTenantIdsByName() {

        List<Long> supplyIds = tenantInfoService.getTenantIdsByName("鲜沐");
        assertFalse(supplyIds.contains(1L));
        List<Long>  fantai= tenantInfoService.getTenantIdsByName("帆台");
        assertFalse(fantai.contains(0L));

        List<Long>  bandIds= tenantInfoService.getTenantIdsByName("鲜沐商城");
        assertTrue(bandIds.contains(620L));


    }
}