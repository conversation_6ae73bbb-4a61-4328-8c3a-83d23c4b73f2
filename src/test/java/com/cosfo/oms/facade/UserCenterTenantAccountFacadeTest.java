package com.cosfo.oms.facade;

import com.cosfo.oms.facade.dto.tenant.TenantAccountInputDTO;
import com.github.pagehelper.PageInfo;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class UserCenterTenantAccountFacadeTest {

    @Resource
    private UserCenterTenantAccountFacade userCenterTenantAccountFacade;

    @Test
    void getTenantAccountInfo() {
        TenantAccountResultResp tenantAccountInfo = userCenterTenantAccountFacade.getTenantAccountInfo(10116L);
        System.out.println(tenantAccountInfo);
    }

    @Test
    void getTenantAccountInfoById() {
        TenantAccountResultResp tenantAccountInfoById = userCenterTenantAccountFacade.getTenantAccountInfoById(10116L);
        System.out.println(tenantAccountInfoById);
    }

    @Test
    void getTenantAccountInfoPage() {
        TenantAccountInputDTO inputDTO = new TenantAccountInputDTO();
        inputDTO.setPageIndex(1);
        inputDTO.setPageSize(10);
        inputDTO.setTenantId(2L);
        PageInfo<TenantAccountResultResp> tenantAccountInfoPage = userCenterTenantAccountFacade.getTenantAccountInfoPage(inputDTO);
        System.out.println(tenantAccountInfoPage);
    }

    @Test
    void delTenantAccount() {
    }

    @Test
    void createTenantAccount() {
    }

    @Test
    void updateTenantAccount() {
    }
}