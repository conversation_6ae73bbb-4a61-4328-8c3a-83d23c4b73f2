#!/bin/bash

# =================================================================
# Poll Pipeline Status Script (Optimized)
#
# Description:
#   This script polls an Aliyun DevOps pipeline run status.
#   It accurately determines the final status by inspecting the
#   'stages' array in the API response.
#
# Success Condition: The stage named "部署" has a status of "SUCCESS".
# Failure Condition: Any stage has a status of "FAIL" or "CANCELED".
#
# Usage:
#   ./poll_pipeline_status.sh <PIPELINE_ID> <PIPELINE_RUN_ID> <API_TOKEN>
# =================================================================

# --- Argument Validation ---
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <PIPELINE_ID> <PIPELINE_RUN_ID> <API_TOKEN>"
    exit 1
fi

PIPELINE_ID=$1
PIPELINE_RUN_ID=$2
API_TOKEN=$3

# --- Configuration ---
TIMEOUT=900        # 15 minutes (900 seconds)
INTERVAL=60        # 1 minute (60 seconds)
START_TIME=$(date +%s)

echo "🚀 开始轮询流水线状态..."
echo "   - Pipeline ID: $PIPELINE_ID"
echo "   - Run ID:      $PIPELINE_RUN_ID"
echo "   - Timeout:     ${TIMEOUT}s"
echo "------------------------------------"

# --- Main Polling Loop ---
while [ $(( $(date +%s) - START_TIME )) -lt $TIMEOUT ]; do
    # Fetch the current status from the API
    STATUS_RESPONSE=$(curl -s -X 'GET' "https://openapi-rdc.aliyuncs.com/oapi/v1/flow/organizations/6189f099041d450d2c253abc/pipelines/$PIPELINE_ID/runs/$PIPELINE_RUN_ID" \
        -H 'Content-Type: application/json' \
        -H "x-yunxiao-token: $API_TOKEN")

    # --- New Status Check Logic ---

    # 1. Check for any failed or canceled stages first (highest priority)
    # The `jq -e` command will exit with status 0 if it finds a match.
    if echo "$STATUS_RESPONSE" | jq -e '.stages[] | .stageInfo.status | select(. == "FAIL" or . == "CANCELED")' > /dev/null; then
        echo "❌ 部署失败！检测到有阶段已失败或取消。"
        # Optional: Find and print the name of the failed stage
        FAILED_STAGE_NAME=$(echo "$STATUS_RESPONSE" | jq -r '.stages[] | select(.stageInfo.status == "FAIL" or .stageInfo.status == "CANCELED") | .name' | head -n 1)
        echo "   - 失败阶段: $FAILED_STAGE_NAME"
        exit 1
    fi

    # 2. If no failures, check if the "部署" stage is successful
    # This will only match if the specific stage "部署" has status "SUCCESS".
    if echo "$STATUS_RESPONSE" | jq -e '.stages[] | select(.name == "部署") | .stageInfo.status | select(. == "SUCCESS")' > /dev/null; then
        echo "✅ 部署成功！'部署'阶段已完成。"
        exit 0
    fi

    # 3. If neither failure nor success conditions are met, it's still running
    echo "⏳ 流水线仍在运行中，将在 ${INTERVAL} 秒后再次检查..."
    sleep $INTERVAL
done

# --- Timeout Handling ---
echo "⚠️ 部署超时！流水线在 ${TIMEOUT} 秒内未达到最终状态。"
exit 2